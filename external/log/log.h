/**
 * Copyright (c) 2020 rxi
 *
 * This library is free software; you can redistribute it and/or modify it
 * under the terms of the MIT license. See `log.c` for details.
 */

#pragma once

#include <errno.h>
#include <stdarg.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <time.h>

#define LOG_VERSION "0.1.0"

typedef struct {
  va_list ap;
  const char *fmt;
  const char *file;
  struct tm *time;
  void *udata;
  int line;
  int level;
} log_Event;

typedef void (*log_LogFn)(log_Event *ev);
typedef void (*log_LockFn)(bool lock, void *udata);

enum { LOG_TRACE, LOG_DEBUG, LOG_INFO, LOG_WARN, LOG_ERROR, LOG_FATAL };

#define log_trace(...) log_log(LOG_TRACE, __FILE__, __LINE__, __VA_ARGS__)
#define log_debug(...) log_log(LOG_DEBUG, __FILE__, __LINE__, __VA_ARGS__)
#define log_info(...) log_log(LOG_INFO, __FILE__, __LINE__, __VA_ARGS__)
#define log_warn(...) log_log(LOG_WARN, __FILE__, __LINE__, __VA_ARGS__)
#define log_error(...) log_log(LOG_ERROR, __FILE__, __LINE__, __VA_ARGS__)
#define log_fatal(...) log_log(LOG_FATAL, __FILE__, __LINE__, __VA_ARGS__)

const char *log_level_string(int level);
void log_set_lock(log_LockFn fn, void *udata);
void log_set_level(int level);
void log_set_quiet(bool enable);
int log_add_callback(log_LogFn fn, void *udata, int level);
int log_add_fp(FILE *fp, int level);

void log_log(int level, const char *file, int line, const char *fmt, ...);

// ADDED CODE
// Everything below was not originally part of the library

// This was added to parse log level from user-given configuration
int string_to_log_level(const char *level_str);

// This was added to replace the `error_reporting` module
// The two below functions :
// * allow modules to report errors
// * let main decide what to log
// This way, logging control is totally given to main,
// while having good granularity for displaying precise errors.

void last_error_set_fn(const char *file, int line, const char *fmt, ...);
#define last_error_set(...) last_error_set_fn(__FILE__, __LINE__, __VA_ARGS__)

void last_error_display_fn(int level);
#define last_error_display(level, ...)                                         \
  {                                                                            \
    log_log(level, __FILE__, __LINE__, __VA_ARGS__);                           \
    last_error_display_fn(level);                                              \
  }

void log_next_is_start();
void log_next_is_end();
void before_log_space();

#define log_start(level, ...)                                                  \
  {                                                                            \
    log_next_is_start();                                                       \
    log_log(level, __FILE__, __LINE__, __VA_ARGS__);                           \
  }

#define log_end(level, ...)                                                    \
  {                                                                            \
    log_next_is_end();                                                         \
    log_log(level, __FILE__, __LINE__, __VA_ARGS__);                           \
  }

#define log_space(level)                                                       \
  before_log_space();                                                          \
  log_log(level, __FILE__, __LINE__, "");

// malloc failure is a pretty common error
#define LOG_CHECK_MALLOC(variable, malloc_instr)                               \
  variable = malloc_instr;                                                     \
  if (!(variable)) {                                                           \
    last_error_set("malloc failed (errno:%d - %s)", errno, strerror(errno));   \
    abort();                                                                   \
  }
