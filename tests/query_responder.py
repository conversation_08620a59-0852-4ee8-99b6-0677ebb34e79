#!/usr/bin/env python3
"""
Script pour répondre automatiquement aux requêtes QRY pendant les tests.
Surveille le répertoire /home/<USER>/querydemog/ et crée automatiquement
les fichiers .answer correspondants aux fichiers .qry.
"""

import os
import time
import sys
import signal
import threading
from pathlib import Path

# Configuration
QUERYDEMOG_DIR = "/home/<USER>/querydemog"
PATIENT_DATA_DIR = "patient_data"
POLL_INTERVAL = 0.5  # Vérifier toutes les 500ms

# Données de test pour le QueryID spécifique
TEST_PATIENT_DATA = {
    "95116888419304127": {
        "queryid": "95116888419304127",
        "status": "OK",
        "temps_ms": "1500",
        "civilite": "M",
        "nom": "<PERSON>",
        "prenom": "<PERSON>",
        "date_naissance": "19900512",
        "sexe": "M"
    }
}

class QueryResponder:
    def __init__(self):
        self.running = False
        self.processed_files = set()
        
    def start(self):
        """Démarre la surveillance du répertoire."""
        self.running = True
        print(f"🔍 Surveillance du répertoire: {QUERYDEMOG_DIR}")
        print("📋 QueryIDs configurés:")
        for query_id in TEST_PATIENT_DATA.keys():
            print(f"  - {query_id}")
        print("⏳ En attente de fichiers .qry...")
        
        while self.running:
            try:
                self.check_for_queries()
                time.sleep(POLL_INTERVAL)
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Erreur: {e}")
                time.sleep(1)
    
    def stop(self):
        """Arrête la surveillance."""
        self.running = False
        print("\n🛑 Arrêt de la surveillance...")
    
    def check_for_queries(self):
        """Vérifie s'il y a de nouveaux fichiers .qry à traiter."""
        if not os.path.exists(QUERYDEMOG_DIR):
            return
            
        for filename in os.listdir(QUERYDEMOG_DIR):
            if filename.endswith('.qry') and filename not in self.processed_files:
                qry_path = os.path.join(QUERYDEMOG_DIR, filename)
                self.process_query_file(qry_path, filename)
                self.processed_files.add(filename)
    
    def process_query_file(self, qry_path, filename):
        """Traite un fichier .qry et crée le fichier .answer correspondant."""
        try:
            # Lire le fichier .qry
            with open(qry_path, 'r') as f:
                content = f.read()
            
            print(f"📄 Fichier .qry trouvé: {filename}")
            print(f"📝 Contenu: {content.strip()}")
            
            # Extraire le QueryID
            query_id = None
            for line in content.split('\n'):
                if line.startswith('queryid='):
                    query_id = line.split('=', 1)[1].strip()
                    break
            
            if not query_id:
                print(f"⚠️  QueryID non trouvé dans {filename}")
                return
            
            print(f"🔍 QueryID extrait: {query_id}")
            
            # Vérifier si nous avons des données pour ce QueryID
            if query_id in TEST_PATIENT_DATA:
                # Créer le fichier .answer
                answer_filename = filename.replace('.qry', '.answer')
                answer_path = os.path.join(QUERYDEMOG_DIR, answer_filename)
                
                patient_data = TEST_PATIENT_DATA[query_id]
                
                with open(answer_path, 'w') as f:
                    for key, value in patient_data.items():
                        f.write(f"{key}={value}\n")
                
                print(f"✅ Fichier .answer créé: {answer_filename}")
                print(f"👤 Données patient: {patient_data['prenom']} {patient_data['nom']}")
            else:
                print(f"❌ Aucune donnée configurée pour QueryID: {query_id}")
                
        except Exception as e:
            print(f"❌ Erreur lors du traitement de {filename}: {e}")

def signal_handler(signum, frame):
    """Gestionnaire de signal pour arrêt propre."""
    print(f"\n🔔 Signal {signum} reçu")
    sys.exit(0)

def main():
    # Configuration des signaux
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Créer le répertoire s'il n'existe pas
    os.makedirs(QUERYDEMOG_DIR, exist_ok=True)
    
    # Démarrer le répondeur
    responder = QueryResponder()
    try:
        responder.start()
    except KeyboardInterrupt:
        pass
    finally:
        responder.stop()

if __name__ == "__main__":
    main()
