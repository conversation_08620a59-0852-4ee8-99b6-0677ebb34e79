#!/bin/bash

# Script de test pour valider la refonte de configuration unifiée

echo "=== Test de build et validation de la refonte de configuration ==="

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "CMakeLists.txt" ]; then
    print_error "CMakeLists.txt non trouvé. Exécutez ce script depuis la racine du projet."
    exit 1
fi

# Créer le répertoire de build
print_status "Création du répertoire de build..."
mkdir -p build
cd build

# Tester différentes combinaisons de build
combinations=(
    "TCP;HL7;FILE_STORING"
    "RS232;POCT;FILE_STORING"
    "TCP;POCT;FILE_STORING"
    "RS232;HL7;FILE_STORING"
)

for combo in "${combinations[@]}"; do
    IFS=';' read -ra LAYERS <<< "$combo"
    TRANSPORT=${LAYERS[0]}
    APPLICATION=${LAYERS[1]}
    STORING=${LAYERS[2]}
    
    print_status "Test de build pour $TRANSPORT + $APPLICATION + $STORING..."
    
    # Nettoyer le build précédent
    rm -rf *
    
    # Configurer avec CMake
    cmake .. -D${TRANSPORT}=ON -D${APPLICATION}=ON -D${STORING}=ON
    
    if [ $? -ne 0 ]; then
        print_error "Échec de la configuration CMake pour $combo"
        continue
    fi
    
    # Build
    make -j$(nproc)
    
    if [ $? -ne 0 ]; then
        print_error "Échec du build pour $combo"
        continue
    fi
    
    print_status "✓ Build réussi pour $TRANSPORT + $APPLICATION + $STORING"
    
    # Tester l'exécutable avec la nouvelle syntaxe
    if [ -f "f200" ]; then
        print_status "Test de l'aide de l'exécutable..."
        ./f200 --help
        
        if [ $? -eq 0 ]; then
            print_status "✓ L'aide s'affiche correctement"
        else
            print_warning "L'aide ne s'affiche pas correctement"
        fi
    fi
done

cd ..

print_status "=== Tests terminés ==="

# Afficher un résumé des changements
echo ""
echo "=== Résumé des changements effectués ==="
echo "1. ✓ Fichier de configuration unifié créé : tests/sample_configs/unified.config"
echo "2. ✓ Nouveau module de parsing : src/unified_config_file.c + include/unified_config_file.h"
echo "3. ✓ Interface CLI modifiée : -c au lieu de -g -t -a -s"
echo "4. ✓ main.c adapté pour utiliser le parser unifié"
echo "5. ✓ CMakeLists.txt mis à jour"
echo "6. ✓ README.md mis à jour avec la nouvelle syntaxe"
echo "7. ✓ Fichiers de test créés pour différentes combinaisons"
echo ""
echo "Nouvelle syntaxe d'utilisation :"
echo "  Ancien : ./f200 -g global.config -t tcp.config -a hl7.config -s storing.config"
echo "  Nouveau : ./f200 -c unified.config"
echo ""
print_status "Refonte terminée avec succès !"
