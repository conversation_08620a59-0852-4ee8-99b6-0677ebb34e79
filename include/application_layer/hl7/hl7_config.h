#pragma once

#define CONFIG_SENDING_APPLICATION "sending-application"
#define CONFIG_SENDING_FACILITY "sending-facility"
#define CONFIG_DELAY_BEFORE_ACK "delay-before-ack"

#define INI_DELAY_BEFORE_ACK -1

typedef struct {
  /**
   * @brief Unique identifier for the process.
   */
  char *sending_application;
  /**
   * @brief Unique identifier for the organization.
   */
  char *sending_facility;
  /**
   * @brief Delay between message reception and acknowledgment sending.
   */
  long int delay_before_ack;
} ApplicationConfiguration;

void application_configuration_init(ApplicationConfiguration *config);
void application_configuration_destruct(ApplicationConfiguration *config);
char *
application_configuration_to_string(const ApplicationConfiguration *config);
