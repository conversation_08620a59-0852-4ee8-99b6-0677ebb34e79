#pragma once

#include "poct.h"  // Ceci devrait fonctionner grâce aux includes globaux
#include <stdlib.h>

// Fonction utilitaire pour trouver un type de message par nom
POCTMessageType get_message_type_by_name(const PoctSpecification *spec, const char *name);

// Handlers génériques pour les messages POCT standards
POCTMessageHandlingResult *handle_patient_list_request(
    const POCTMessage *message, 
    void *context,
    const PoctSpecification *spec);

POCTMessageHandlingResult *handle_patient_list_item(
    const POCTMessage *message,
    void *context, 
    const PoctSpecification *spec);
