#pragma once

#include "poct_config.h"
#include "pubsub.h"
#include "poct.h"
#include <stdbool.h>

// Interface générique pour les handlers POCT
typedef struct POCTDeviceDriver {
    const char *device_name;
    const PoctSpecification *specification;
    POCTConversationHandler *(*create_handler)(void);
    void (*destroy_handler)(POCTConversationHandler *handler);
} POCTDeviceDriver;

// Fonctions pour l'enregistrement des drivers
int poct_register_device_driver(const POCTDeviceDriver *driver);
const POCTDeviceDriver *poct_get_current_driver(void);

// Fonctions publiques de la couche POCT
int application_layer_init(ApplicationConfiguration *init_config,
                           PubSub *init_transport2application,
                           PubSub *init_application2transport,
                           PubSub *init_application2storing);

void application_layer_process_message(void *void_context,
                                       const void *void_data);

void application_layer_end();
