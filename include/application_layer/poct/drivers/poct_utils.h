#pragma once

#include "poct.h"

const char *generate_control_id();

xmlNodePtr find_direct_child(xmlNodePtr parent, const char *child_name);

xmlNodePtr *find_direct_children(xmlNodePtr parent, const char *child_name);

xmlNodePtr get_node_by_path(xmlNodePtr root, const char *path);

char *get_attribute_value_by_path(xmlNodePtr root, const char *path,
                                  const char *attr_name);

POCTMessage **generate_response_list(size_t count, ...);

xmlDocPtr generate_document_with_header(const char *message_name);

POCTMessage *generate_document_with_children(const char *message_name,
                                             const PoctSpecification *spec,
                                             int num_children, ...);

POCTMessage *generate_acknowledgment(bool success,
                                     const POCTMessage *acknowledged_message,
                                     const PoctSpecification *spec);
