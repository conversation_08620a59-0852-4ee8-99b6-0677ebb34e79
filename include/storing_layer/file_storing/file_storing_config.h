#pragma once

#define CONFIG_STORING_DIRECTORY "storing-dir"

#define INI_DELAY_BEFORE_ACK -1

typedef struct {
  /**
   * @brief Directory path where files will be stored.
   */
  char *storing_directory;
} StoringConfiguration;

void storing_configuration_init(StoringConfiguration *config);
void storing_configuration_destruct(StoringConfiguration *config);
char *storing_configuration_to_string(const StoringConfiguration *config);
