#pragma once

#define CONFIG_BIND_IP "bind-ip"
#define CONFIG_BIND_PORT "bind-port"
#define CONFIG_RECV_TIMEOUT "recv-timeout"

#define INI_PORT -1
#define INI_RECV_TIMEOUT -2 // -1 => wait forever

typedef struct {
  /**
   * @brief IP address to bind to.
   */
  char *bind_ip;
  /**
   * @brief Port number to bind to.
   */
  long int bind_port;
  /**
   * @brief Maximum delay between connection request acceptation and first
   * message. After that delay is over, this programm will close the connection.
   */
  long int recv_timeout;
} TransportConfiguration;

void transport_configuration_init(TransportConfiguration *config);
void transport_configuration_destruct(TransportConfiguration *config);
char *transport_configuration_to_string(const TransportConfiguration *config);
