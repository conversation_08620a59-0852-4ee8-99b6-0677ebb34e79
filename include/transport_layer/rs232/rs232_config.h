#pragma once

#include <libserialport.h>
#include <sys/types.h>

#define CONFIG_BAUD_RATE "baud-rate"
#define CONFIG_PORT_NAME "port-name"
#define CONFIG_BITS "bits"
#define CONFIG_PARITY "parity"
#define CONFIG_FLOW_CONTROL "flow-control"
#define CONFIG_STOP_BITS "stop-bits"

#define INI_BAUD_RATE -1
#define INI_BITS -1
#define INI_FLOW_CONTROl 100
#define INI_PARITY 100
#define INI_STOP_BITS -1

typedef struct {
  char *port_name;
  ssize_t baud_rate;
  ssize_t bits;
  enum sp_parity parity;
  ssize_t stop_bits;
  enum sp_flowcontrol flow_control;
} TransportConfiguration;

void transport_configuration_init(TransportConfiguration *config);
void transport_configuration_destruct(TransportConfiguration *config);
char *transport_configuration_to_string(const TransportConfiguration *config);
