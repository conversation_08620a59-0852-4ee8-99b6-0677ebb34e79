# **STAGE 1: Build the C Program**
FROM registry.access.redhat.com/ubi9/ubi:latest as build

# Mettre à jour le système et installer les outils nécessaires
RUN echo "Updating system" && dnf update -y && \
	dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm && \
	dnf install -y dnf-plugins-core && \
	dnf clean all

# Activation des dépôts supplémentaires
RUN echo "Activating repository" && dnf config-manager --set-enabled ubi-9-baseos-rpms ubi-9-appstream-rpms

# Installation des dépendances nécessaires pour la compilation (si nécessaire)
RUN echo "Installing gcc" && dnf install -y gcc && \
	echo "Installing gcc-c++" && dnf install -y gcc-c++ && \
	echo "Installing make" && dnf install -y make && \
	echo "Installing cmake" && dnf install -y cmake && \
	echo "Installing cppcheck" && dnf install -y cppcheck && \
    echo "Installing autoconf" && dnf install -y autoconf && \
    echo "Installing automake" && dnf install -y automake && \
    echo "Installing libtool" && dnf install -y libtool && \
    echo "Installing pkgconfig" && dnf install -y pkgconfig && \
    echo "Installing git" && dnf install -y git && \
    echo "Installing sudo" && dnf install -y sudo && \	
	echo "Installing libxml2" && dnf install -y libxml2 && \
	echo "Installing libxml2-devel" && dnf install -y libxml2-devel && \
	echo "Installing libserialport-devel" && dnf install -y libserialport-devel && \
	echo "Installing wget" && dnf install -y wget
	
# Nettoyer le cache DNF
RUN dnf clean all

# Vérifier l'installation de CMake
RUN cmake --version

# Creation du workdir
WORKDIR /driversynlab

# Copie du fichier source
COPY drivers_automates.tar.gz ./

# Decompresse et install
RUN tar -xzvf drivers_automates.tar.gz && \
    rm drivers_automates.tar.gz && \
    mv drivers_automates/* . && \
    rm -rf drivers_automates

# Compilation
RUN echo "Starting compilation process" && \
    mkdir build && cd build && \
    echo "Build directory created, starting cmake" && \
    cmake .. && \
    echo "CMAKE completed, starting make" && \
    make && \
    echo "Make completed"

# Actions supplémentaires
RUN cd /driversynlab/build/bin && \
    mkdir config_files && \
    cp ../../tests/sample_configs/* config_files/ && \
    chmod -R 755 config_files && \
    ls -la config_files/ 
    #sed -i 's/bind-ip = "127.0.0.1"/bind-ip = "0.0.0.0"/' config_files/unified.config && \
    #sed -i 's/bind-port = 1234/bind-port = 50901/' config_files/unified.config

# Creation du conteneur final sans les paquets nécessaire à la compilation
FROM registry.access.redhat.com/ubi9/ubi-init

RUN echo "Updating system" && dnf update -y && dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm && \
		dnf install -y libxml2 libserialport && \
        dnf clean all

# Récupérer le programme compilé
COPY --from=build /driversynlab/build/bin/ /usr/local/bin/ 
COPY --from=build /driversynlab/build/lib/ /usr/local/lib/
COPY --from=build /driversynlab/build/external/ /usr/local/lib/ 

# Point d'entrée du conteneur
ENTRYPOINT ["/usr/local/bin/f200", "-c", "/usr/local/bin/config_files/unified.config"]

# Port expose
EXPOSE 50901
