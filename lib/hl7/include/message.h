/**
 * @file message.h
 * @brief HL7 "Message" object implementation
 *
 * A message is a list of segments.
 */

#pragma once

#include "segment.h"
#include <stdbool.h>

typedef struct MessageStruct {
  DynamicList *segments;
  Separators separators;
} Message;

/**
 * @brief Creates a message.
 *
 * @return
 * - Success: pointer to the created message
 * - Error: NULL
 */
Message *hl7_message_create();

/**
 * @brief Parses a string, creating a new message.
 *
 * @return
 * - Success: pointer to the created message
 * - Error: NULL
 */
Message *hl7_message_from_string(const char *str);

/**
 * @brief Generates a readable string representing a message.
 *
 * @param message
 *
 * @return string representation of the message
 */
char *hl7_message_to_string(const Message *message);

/**
 * @brief Adds a copy of a segment to the message.
 *
 * The sent segment is considered owned by the message
 *
 * @param message
 * @param segment segment to add
 *
 * @return
 * - Success: 0
 * - Error: 1
 */
int hl7_message_add_segment(Message *message, Segment *segment);

/**
 * @brief Returns a segment by its position in the list of segments.
 *
 *
 * @param message
 * @param index Segment's position in the list. The first segment is at
 * position 0.
 *
 * @return
 * - Success : the segment
 * - Error : NULL
 */
const Segment *hl7_message_get_segment_by_index(const Message *message,
                                                size_t index);

/**
 * @brief Returns a segment by its type.
 *
 * @param message
 * @param type segment type to look for
 *
 * @return a segment if the corresponding type was found ; NULL otherwise.
 */
const Segment *hl7_message_get_segment_by_type(const Message *message,
                                               const SegmentType type);

/**
 * @brief Returns the number of segments in the message.
 *
 * @param message
 *
 * @return The number of segments in the message.
 */
size_t hl7_message_get_segments_count(const Message *message);

/**
 * @brief Destroys a message, freeing its allocated ressources.
 *
 * @param message
 */
void hl7_message_destruct(Message *message);
