#pragma once

#include "common.h"
#include "specification.h"

#if HL7_VERSION == 260

extern const FieldInSegmentSpec QRD_Spec[12];

typedef enum {
  QRDQueryDateTime = 0,
  QRDQueryFormatCode = 1,
  QRDQueryPriority = 2,
  QRDQueryID = 3,
  QRDDeferredResponseType = 4,
  QRDDeferredResponseDateTime = 5,
  QRDQuantityLimitedRequest = 6,
  QRDWhoSubjectFilter = 7,
  QRDWhatSubjectFilter = 8,
  QRDWhatDepartmentDataCode = 9,
  QRDWhatDataCodeValueQual = 10,
  QRDQueryResultsLevel = 11
} QRDFieldPosition;

#elif HL7_VERSION == 250

extern const FieldInSegmentSpec QRD_Spec[12];

typedef enum {
  QRDQueryDateTime = 0,
  QRDQueryFormatCode = 1,
  QRDQueryPriority = 2,
  QRDQueryID = 3,
  QRDDeferredResponseType = 4,
  QRDDeferredResponseDateTime = 5,
  QRDQuantityLimitedRequest = 6,
  QRDWhoSubjectFilter = 7,
  QRDWhatSubjectFilter = 8,
  QRDWhatDepartmentDataCode = 9,
  QRDWhatDataCodeValueQual = 10,
  QRDQueryResultsLevel = 11
} QRDFieldPosition;

#elif HL7_VERSION == 240

extern const FieldInSegmentSpec QRD_Spec[10];

typedef enum {
  QRDQueryDateTime = 0,
  QRDQueryFormatCode = 1,
  QRDQueryPriority = 2,
  QRDQueryID = 3,
  QRDDeferredResponseType = 4,
  QRDDeferredResponseDateTime = 5,
  QRDQuantityLimitedRequest = 6,
  QRDWhoSubjectFilter = 7,
  QRDWhatSubjectFilter = 8,
  QRDWhatDepartmentDataCode = 9
} QRDFieldPosition;

#endif