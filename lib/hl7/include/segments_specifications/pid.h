#pragma once

#include "common.h"
#include "specification.h"

#if HL7_VERSION == 240

extern const FieldInSegmentSpec PID_Spec[38];

typedef enum {
  PIDSetID = 0,
  PIDPatientID = 1,
  PIDPatientIdentifierList = 2,
  PIDAlternatePatientID = 3,
  PIDPatientName = 4,
  PIDMothersMaidenName = 5,
  PIDDateTimeOfBirth = 6,
  PIDAdministrativeSex = 7,
  PIDPatientAlias = 8,
  PIDRace = 9,
  PIDPatientAddress = 10,
  PIDCountyCode = 11,
  PIDPhoneNumberHome = 12,
  PIDPhoneNumberBusiness = 13,
  PIDPrimaryLanguage = 14,
  PIDMaritalStatus = 15,
  PIDReligion = 16,
  PIDPatientAccountNumber = 17,
  PIDSSNNumberPatient = 18,
  PIDDriverLicensePatient = 19,
  PIDMothersIdentifier = 20,
  PIDEthnicGroup = 21,
  PIDBirthPlace = 22,
  PIDMultipleBirthIndicator = 23,
  PIDBirthOrder = 24,
  PIDCitizenship = 25,
  PIDVeteransMilitaryStatus = 26,
  PIDNationality = 27,
  PIDPatientDeathDateAndTime = 28,
  PIDPatientDeathIndicator = 29,
  PIDIdentityUnknownIndicator = 30,
  PIDIdentityReliabilityCode = 31,
  PIDLastUpdateDateTime = 32,
  PIDLastUpdateFacility = 33,
  PIDSpeciesCode = 34,
  PIDBreedCode = 35,
  PIDStrain = 36,
  PIDProductionClassCode = 37
} PIDFieldPosition;

#elif HL7_VERSION == 250 || HL7_VERSION == 260

extern const FieldInSegmentSpec PID_Spec[39];

typedef enum {
  PIDSetID = 0,
  PIDPatientID = 1,
  PIDPatientIdentifierList = 2,
  PIDAlternatePatientID = 3,
  PIDPatientName = 4,
  PIDMothersMaidenName = 5,
  PIDDateTimeOfBirth = 6,
  PIDAdministrativeSex = 7,
  PIDPatientAlias = 8,
  PIDRace = 9,
  PIDPatientAddress = 10,
  PIDCountyCode = 11,
  PIDPhoneNumberHome = 12,
  PIDPhoneNumberBusiness = 13,
  PIDPrimaryLanguage = 14,
  PIDMaritalStatus = 15,
  PIDReligion = 16,
  PIDPatientAccountNumber = 17,
  PIDSSNNumberPatient = 18,
  PIDDriverLicensePatient = 19,
  PIDMothersIdentifier = 20,
  PIDEthnicGroup = 21,
  PIDBirthPlace = 22,
  PIDMultipleBirthIndicator = 23,
  PIDBirthOrder = 24,
  PIDCitizenship = 25,
  PIDVeteransMilitaryStatus = 26,
  PIDNationality = 27,
  PIDPatientDeathDateAndTime = 28,
  PIDPatientDeathIndicator = 29,
  PIDIdentityUnknownIndicator = 30,
  PIDIdentityReliabilityCode = 31,
  PIDLastUpdateDateTime = 32,
  PIDLastUpdateFacility = 33,
  PIDSpeciesCode = 34,
  PIDBreedCode = 35,
  PIDStrain = 36,
  PIDProductionClassCode = 37,
  PIDTribalCitizenship = 38
} PIDFieldPosition;

#endif
