/**
 * @file segment_enum.h
 * @brief Segment types enum handling.
 */

#pragma once

#include "common.h"
#include <stddef.h>
#include <string.h>

/// see :
/// * https://stackoverflow.com/a/10966395/13123535
/// * https://stackoverflow.com/a/16844938/13123535

// segment types are backward compatible

#if HL7_VERSION == 240
#define FOREACH_SEGMENT_TYPE(TRANSFORM)                                        \
  TRANSFORM(ABS)                                                               \
  TRANSFORM(ACC)                                                               \
  TRANSFORM(ADD)                                                               \
  TRANSFORM(AFF)                                                               \
  TRANSFORM(AIG)                                                               \
  TRANSFORM(AIL)                                                               \
  TRANSFORM(AIP)                                                               \
  TRANSFORM(AIS)                                                               \
  TRANSFORM(AL1)                                                               \
  TRANSFORM(APR)                                                               \
  TRANSFORM(ARQ)                                                               \
  TRANSFORM(AUT)                                                               \
  TRANSFORM(BHS)                                                               \
  TRANSFORM(BLC)                                                               \
  TRANSFORM(BLG)                                                               \
  TRANSFORM(BTS)                                                               \
  TRANSFORM(CDM)                                                               \
  TRANSFORM(CM0)                                                               \
  TRANSFORM(CM1)                                                               \
  TRANSFORM(CM2)                                                               \
  TRANSFORM(CNS)                                                               \
  TRANSFORM(CSP)                                                               \
  TRANSFORM(CSR)                                                               \
  TRANSFORM(CSS)                                                               \
  TRANSFORM(CTD)                                                               \
  TRANSFORM(CTI)                                                               \
  TRANSFORM(DB1)                                                               \
  TRANSFORM(DG1)                                                               \
  TRANSFORM(DRG)                                                               \
  TRANSFORM(DSC)                                                               \
  TRANSFORM(DSP)                                                               \
  TRANSFORM(ECD)                                                               \
  TRANSFORM(ECR)                                                               \
  TRANSFORM(EDU)                                                               \
  TRANSFORM(EQP)                                                               \
  TRANSFORM(EQU)                                                               \
  TRANSFORM(ERR)                                                               \
  TRANSFORM(EVN)                                                               \
  TRANSFORM(FAC)                                                               \
  TRANSFORM(FHS)                                                               \
  TRANSFORM(FT1)                                                               \
  TRANSFORM(FTS)                                                               \
  TRANSFORM(GOL)                                                               \
  TRANSFORM(GP1)                                                               \
  TRANSFORM(GP2)                                                               \
  TRANSFORM(GT1)                                                               \
  TRANSFORM(IAM)                                                               \
  TRANSFORM(IN1)                                                               \
  TRANSFORM(IN2)                                                               \
  TRANSFORM(IN3)                                                               \
  TRANSFORM(INV)                                                               \
  TRANSFORM(ISD)                                                               \
  TRANSFORM(LAN)                                                               \
  TRANSFORM(LCC)                                                               \
  TRANSFORM(LCH)                                                               \
  TRANSFORM(LDP)                                                               \
  TRANSFORM(LOC)                                                               \
  TRANSFORM(LRL)                                                               \
  TRANSFORM(MFA)                                                               \
  TRANSFORM(MFE)                                                               \
  TRANSFORM(MFI)                                                               \
  TRANSFORM(MRG)                                                               \
  TRANSFORM(MSA)                                                               \
  TRANSFORM(MSH)                                                               \
  TRANSFORM(NCK)                                                               \
  TRANSFORM(NDS)                                                               \
  TRANSFORM(NK1)                                                               \
  TRANSFORM(NPU)                                                               \
  TRANSFORM(NSC)                                                               \
  TRANSFORM(NST)                                                               \
  TRANSFORM(NTE)                                                               \
  TRANSFORM(OBR)                                                               \
  TRANSFORM(OBX)                                                               \
  TRANSFORM(ODS)                                                               \
  TRANSFORM(ODT)                                                               \
  TRANSFORM(OM1)                                                               \
  TRANSFORM(OM2)                                                               \
  TRANSFORM(OM3)                                                               \
  TRANSFORM(OM4)                                                               \
  TRANSFORM(OM5)                                                               \
  TRANSFORM(OM6)                                                               \
  TRANSFORM(OM7)                                                               \
  TRANSFORM(ORC)                                                               \
  TRANSFORM(ORG)                                                               \
  TRANSFORM(PCR)                                                               \
  TRANSFORM(PD1)                                                               \
  TRANSFORM(PDA)                                                               \
  TRANSFORM(PDC)                                                               \
  TRANSFORM(PEO)                                                               \
  TRANSFORM(PES)                                                               \
  TRANSFORM(PID)                                                               \
  TRANSFORM(PR1)                                                               \
  TRANSFORM(PRA)                                                               \
  TRANSFORM(PRB)                                                               \
  TRANSFORM(PRC)                                                               \
  TRANSFORM(PRD)                                                               \
  TRANSFORM(PSH)                                                               \
  TRANSFORM(PTH)                                                               \
  TRANSFORM(PV1)                                                               \
  TRANSFORM(PV2)                                                               \
  TRANSFORM(QAK)                                                               \
  TRANSFORM(QID)                                                               \
  TRANSFORM(QPD)                                                               \
  TRANSFORM(QRD)                                                               \
  TRANSFORM(QRF)                                                               \
  TRANSFORM(QRI)                                                               \
  TRANSFORM(RCP)                                                               \
  TRANSFORM(RDF)                                                               \
  TRANSFORM(RDT)                                                               \
  TRANSFORM(RF1)                                                               \
  TRANSFORM(RGS)                                                               \
  TRANSFORM(RMI)                                                               \
  TRANSFORM(ROL)                                                               \
  TRANSFORM(RQ1)                                                               \
  TRANSFORM(RQD)                                                               \
  TRANSFORM(RXA)                                                               \
  TRANSFORM(RXC)                                                               \
  TRANSFORM(RXD)                                                               \
  TRANSFORM(RXE)                                                               \
  TRANSFORM(RXG)                                                               \
  TRANSFORM(RXO)                                                               \
  TRANSFORM(RXR)                                                               \
  TRANSFORM(SAC)                                                               \
  TRANSFORM(SCH)                                                               \
  TRANSFORM(SID)                                                               \
  TRANSFORM(STF)                                                               \
  TRANSFORM(TCC)                                                               \
  TRANSFORM(TCD)                                                               \
  TRANSFORM(TXA)                                                               \
  TRANSFORM(UB1)                                                               \
  TRANSFORM(UB2)                                                               \
  TRANSFORM(URD)                                                               \
  TRANSFORM(URS)                                                               \
  TRANSFORM(VAR)                                                               \
  TRANSFORM(ZL7)
#elif HL7_VERSION == 250
#define FOREACH_SEGMENT_TYPE(TRANSFORM)                                        \
  TRANSFORM(ABS)                                                               \
  TRANSFORM(ACC)                                                               \
  TRANSFORM(ADD)                                                               \
  TRANSFORM(AFF)                                                               \
  TRANSFORM(AIG)                                                               \
  TRANSFORM(AIL)                                                               \
  TRANSFORM(AIP)                                                               \
  TRANSFORM(AIS)                                                               \
  TRANSFORM(AL1)                                                               \
  TRANSFORM(APR)                                                               \
  TRANSFORM(ARQ)                                                               \
  TRANSFORM(AUT)                                                               \
  TRANSFORM(BHS)                                                               \
  TRANSFORM(BLC)                                                               \
  TRANSFORM(BLG)                                                               \
  TRANSFORM(BTS)                                                               \
  TRANSFORM(CDM)                                                               \
  TRANSFORM(CM0)                                                               \
  TRANSFORM(CM1)                                                               \
  TRANSFORM(CM2)                                                               \
  TRANSFORM(CNS)                                                               \
  TRANSFORM(CSP)                                                               \
  TRANSFORM(CSR)                                                               \
  TRANSFORM(CSS)                                                               \
  TRANSFORM(CTD)                                                               \
  TRANSFORM(CTI)                                                               \
  TRANSFORM(DB1)                                                               \
  TRANSFORM(DG1)                                                               \
  TRANSFORM(DRG)                                                               \
  TRANSFORM(DSC)                                                               \
  TRANSFORM(DSP)                                                               \
  TRANSFORM(ECD)                                                               \
  TRANSFORM(ECR)                                                               \
  TRANSFORM(EDU)                                                               \
  TRANSFORM(EQP)                                                               \
  TRANSFORM(EQU)                                                               \
  TRANSFORM(ERR)                                                               \
  TRANSFORM(EVN)                                                               \
  TRANSFORM(FAC)                                                               \
  TRANSFORM(FHS)                                                               \
  TRANSFORM(FT1)                                                               \
  TRANSFORM(FTS)                                                               \
  TRANSFORM(GOL)                                                               \
  TRANSFORM(GP1)                                                               \
  TRANSFORM(GP2)                                                               \
  TRANSFORM(GT1)                                                               \
  TRANSFORM(IAM)                                                               \
  TRANSFORM(IN1)                                                               \
  TRANSFORM(IN2)                                                               \
  TRANSFORM(IN3)                                                               \
  TRANSFORM(INV)                                                               \
  TRANSFORM(ISD)                                                               \
  TRANSFORM(LAN)                                                               \
  TRANSFORM(LCC)                                                               \
  TRANSFORM(LCH)                                                               \
  TRANSFORM(LDP)                                                               \
  TRANSFORM(LOC)                                                               \
  TRANSFORM(LRL)                                                               \
  TRANSFORM(MFA)                                                               \
  TRANSFORM(MFE)                                                               \
  TRANSFORM(MFI)                                                               \
  TRANSFORM(MRG)                                                               \
  TRANSFORM(MSA)                                                               \
  TRANSFORM(MSH)                                                               \
  TRANSFORM(NCK)                                                               \
  TRANSFORM(NDS)                                                               \
  TRANSFORM(NK1)                                                               \
  TRANSFORM(NPU)                                                               \
  TRANSFORM(NSC)                                                               \
  TRANSFORM(NST)                                                               \
  TRANSFORM(NTE)                                                               \
  TRANSFORM(OBR)                                                               \
  TRANSFORM(OBX)                                                               \
  TRANSFORM(ODS)                                                               \
  TRANSFORM(ODT)                                                               \
  TRANSFORM(OM1)                                                               \
  TRANSFORM(OM2)                                                               \
  TRANSFORM(OM3)                                                               \
  TRANSFORM(OM4)                                                               \
  TRANSFORM(OM5)                                                               \
  TRANSFORM(OM6)                                                               \
  TRANSFORM(OM7)                                                               \
  TRANSFORM(ORC)                                                               \
  TRANSFORM(ORG)                                                               \
  TRANSFORM(PCR)                                                               \
  TRANSFORM(PD1)                                                               \
  TRANSFORM(PDA)                                                               \
  TRANSFORM(PDC)                                                               \
  TRANSFORM(PEO)                                                               \
  TRANSFORM(PES)                                                               \
  TRANSFORM(PID)                                                               \
  TRANSFORM(PR1)                                                               \
  TRANSFORM(PRA)                                                               \
  TRANSFORM(PRB)                                                               \
  TRANSFORM(PRC)                                                               \
  TRANSFORM(PRD)                                                               \
  TRANSFORM(PSH)                                                               \
  TRANSFORM(PTH)                                                               \
  TRANSFORM(PV1)                                                               \
  TRANSFORM(PV2)                                                               \
  TRANSFORM(QAK)                                                               \
  TRANSFORM(QID)                                                               \
  TRANSFORM(QPD)                                                               \
  TRANSFORM(QRD)                                                               \
  TRANSFORM(QRF)                                                               \
  TRANSFORM(QRI)                                                               \
  TRANSFORM(RCP)                                                               \
  TRANSFORM(RDF)                                                               \
  TRANSFORM(RDT)                                                               \
  TRANSFORM(RF1)                                                               \
  TRANSFORM(RGS)                                                               \
  TRANSFORM(RMI)                                                               \
  TRANSFORM(ROL)                                                               \
  TRANSFORM(RQ1)                                                               \
  TRANSFORM(RQD)                                                               \
  TRANSFORM(RXA)                                                               \
  TRANSFORM(RXC)                                                               \
  TRANSFORM(RXD)                                                               \
  TRANSFORM(RXE)                                                               \
  TRANSFORM(RXG)                                                               \
  TRANSFORM(RXO)                                                               \
  TRANSFORM(RXR)                                                               \
  TRANSFORM(SAC)                                                               \
  TRANSFORM(SCH)                                                               \
  TRANSFORM(SID)                                                               \
  TRANSFORM(STF)                                                               \
  TRANSFORM(TCC)                                                               \
  TRANSFORM(TCD)                                                               \
  TRANSFORM(TXA)                                                               \
  TRANSFORM(UB1)                                                               \
  TRANSFORM(UB2)                                                               \
  TRANSFORM(URD)                                                               \
  TRANSFORM(URS)                                                               \
  TRANSFORM(VAR)                                                               \
  TRANSFORM(ZL7)                                                               \
  TRANSFORM(BPO)                                                               \
  TRANSFORM(BPX)                                                               \
  TRANSFORM(BTX)                                                               \
  TRANSFORM(CER)                                                               \
  TRANSFORM(CON)                                                               \
  TRANSFORM(IIM)                                                               \
  TRANSFORM(IPC)                                                               \
  TRANSFORM(OVR)                                                               \
  TRANSFORM(SFT)                                                               \
  TRANSFORM(SPM)                                                               \
  TRANSFORM(TQ1)                                                               \
  TRANSFORM(TQ2)
#elif HL7_VERSION == 260
#define FOREACH_SEGMENT_TYPE(TRANSFORM)                                        \
  TRANSFORM(ABS)                                                               \
  TRANSFORM(ACC)                                                               \
  TRANSFORM(ADD)                                                               \
  TRANSFORM(AFF)                                                               \
  TRANSFORM(AIG)                                                               \
  TRANSFORM(AIL)                                                               \
  TRANSFORM(AIP)                                                               \
  TRANSFORM(AIS)                                                               \
  TRANSFORM(AL1)                                                               \
  TRANSFORM(APR)                                                               \
  TRANSFORM(ARQ)                                                               \
  TRANSFORM(AUT)                                                               \
  TRANSFORM(BHS)                                                               \
  TRANSFORM(BLC)                                                               \
  TRANSFORM(BLG)                                                               \
  TRANSFORM(BTS)                                                               \
  TRANSFORM(CDM)                                                               \
  TRANSFORM(CM0)                                                               \
  TRANSFORM(CM1)                                                               \
  TRANSFORM(CM2)                                                               \
  TRANSFORM(CNS)                                                               \
  TRANSFORM(CSP)                                                               \
  TRANSFORM(CSR)                                                               \
  TRANSFORM(CSS)                                                               \
  TRANSFORM(CTD)                                                               \
  TRANSFORM(CTI)                                                               \
  TRANSFORM(DB1)                                                               \
  TRANSFORM(DG1)                                                               \
  TRANSFORM(DRG)                                                               \
  TRANSFORM(DSC)                                                               \
  TRANSFORM(DSP)                                                               \
  TRANSFORM(ECD)                                                               \
  TRANSFORM(ECR)                                                               \
  TRANSFORM(EDU)                                                               \
  TRANSFORM(EQP)                                                               \
  TRANSFORM(EQU)                                                               \
  TRANSFORM(ERR)                                                               \
  TRANSFORM(EVN)                                                               \
  TRANSFORM(FAC)                                                               \
  TRANSFORM(FHS)                                                               \
  TRANSFORM(FT1)                                                               \
  TRANSFORM(FTS)                                                               \
  TRANSFORM(GOL)                                                               \
  TRANSFORM(GP1)                                                               \
  TRANSFORM(GP2)                                                               \
  TRANSFORM(GT1)                                                               \
  TRANSFORM(IAM)                                                               \
  TRANSFORM(IN1)                                                               \
  TRANSFORM(IN2)                                                               \
  TRANSFORM(IN3)                                                               \
  TRANSFORM(INV)                                                               \
  TRANSFORM(ISD)                                                               \
  TRANSFORM(LAN)                                                               \
  TRANSFORM(LCC)                                                               \
  TRANSFORM(LCH)                                                               \
  TRANSFORM(LDP)                                                               \
  TRANSFORM(LOC)                                                               \
  TRANSFORM(LRL)                                                               \
  TRANSFORM(MFA)                                                               \
  TRANSFORM(MFE)                                                               \
  TRANSFORM(MFI)                                                               \
  TRANSFORM(MRG)                                                               \
  TRANSFORM(MSA)                                                               \
  TRANSFORM(MSH)                                                               \
  TRANSFORM(NCK)                                                               \
  TRANSFORM(NDS)                                                               \
  TRANSFORM(NK1)                                                               \
  TRANSFORM(NPU)                                                               \
  TRANSFORM(NSC)                                                               \
  TRANSFORM(NST)                                                               \
  TRANSFORM(NTE)                                                               \
  TRANSFORM(OBR)                                                               \
  TRANSFORM(OBX)                                                               \
  TRANSFORM(ODS)                                                               \
  TRANSFORM(ODT)                                                               \
  TRANSFORM(OM1)                                                               \
  TRANSFORM(OM2)                                                               \
  TRANSFORM(OM3)                                                               \
  TRANSFORM(OM4)                                                               \
  TRANSFORM(OM5)                                                               \
  TRANSFORM(OM6)                                                               \
  TRANSFORM(OM7)                                                               \
  TRANSFORM(ORC)                                                               \
  TRANSFORM(ORG)                                                               \
  TRANSFORM(PCR)                                                               \
  TRANSFORM(PD1)                                                               \
  TRANSFORM(PDA)                                                               \
  TRANSFORM(PDC)                                                               \
  TRANSFORM(PEO)                                                               \
  TRANSFORM(PES)                                                               \
  TRANSFORM(PID)                                                               \
  TRANSFORM(PR1)                                                               \
  TRANSFORM(PRA)                                                               \
  TRANSFORM(PRB)                                                               \
  TRANSFORM(PRC)                                                               \
  TRANSFORM(PRD)                                                               \
  TRANSFORM(PSH)                                                               \
  TRANSFORM(PTH)                                                               \
  TRANSFORM(PV1)                                                               \
  TRANSFORM(PV2)                                                               \
  TRANSFORM(QAK)                                                               \
  TRANSFORM(QID)                                                               \
  TRANSFORM(QPD)                                                               \
  TRANSFORM(QRD)                                                               \
  TRANSFORM(QRF)                                                               \
  TRANSFORM(QRI)                                                               \
  TRANSFORM(RCP)                                                               \
  TRANSFORM(RDF)                                                               \
  TRANSFORM(RDT)                                                               \
  TRANSFORM(RF1)                                                               \
  TRANSFORM(RGS)                                                               \
  TRANSFORM(RMI)                                                               \
  TRANSFORM(ROL)                                                               \
  TRANSFORM(RQ1)                                                               \
  TRANSFORM(RQD)                                                               \
  TRANSFORM(RXA)                                                               \
  TRANSFORM(RXC)                                                               \
  TRANSFORM(RXD)                                                               \
  TRANSFORM(RXE)                                                               \
  TRANSFORM(RXG)                                                               \
  TRANSFORM(RXO)                                                               \
  TRANSFORM(RXR)                                                               \
  TRANSFORM(SAC)                                                               \
  TRANSFORM(SCH)                                                               \
  TRANSFORM(SID)                                                               \
  TRANSFORM(STF)                                                               \
  TRANSFORM(TCC)                                                               \
  TRANSFORM(TCD)                                                               \
  TRANSFORM(TXA)                                                               \
  TRANSFORM(UB1)                                                               \
  TRANSFORM(UB2)                                                               \
  TRANSFORM(URD)                                                               \
  TRANSFORM(URS)                                                               \
  TRANSFORM(VAR)                                                               \
  TRANSFORM(ZL7)                                                               \
  TRANSFORM(BPO)                                                               \
  TRANSFORM(BPX)                                                               \
  TRANSFORM(BTX)                                                               \
  TRANSFORM(CER)                                                               \
  TRANSFORM(CON)                                                               \
  TRANSFORM(IIM)                                                               \
  TRANSFORM(IPC)                                                               \
  TRANSFORM(OVR)                                                               \
  TRANSFORM(SFT)                                                               \
  TRANSFORM(TQ1)                                                               \
  TRANSFORM(TQ2)                                                               \
  TRANSFORM(ADJ)                                                               \
  TRANSFORM(ARV)                                                               \
  TRANSFORM(DMI)                                                               \
  TRANSFORM(ILT)                                                               \
  TRANSFORM(IPR)                                                               \
  TRANSFORM(ITM)                                                               \
  TRANSFORM(IVC)                                                               \
  TRANSFORM(IVT)                                                               \
  TRANSFORM(PCE)                                                               \
  TRANSFORM(PKG)                                                               \
  TRANSFORM(PMT)                                                               \
  TRANSFORM(PSG)                                                               \
  TRANSFORM(PSL)                                                               \
  TRANSFORM(PSS)                                                               \
  TRANSFORM(PYE)                                                               \
  TRANSFORM(REL)                                                               \
  TRANSFORM(RFI)                                                               \
  TRANSFORM(SCD)                                                               \
  TRANSFORM(SCP)                                                               \
  TRANSFORM(SDD)                                                               \
  TRANSFORM(SLT)                                                               \
  TRANSFORM(SPM)                                                               \
  TRANSFORM(STZ)                                                               \
  TRANSFORM(UAC)                                                               \
  TRANSFORM(VND)
#else
#error "unhandled HL7 version"
#endif

#define GENERATE_ENUM_SEGMENT(ENUM) SegmentType##ENUM,

/**
 * @brief Enum for all segments types in hl7.
 */
typedef enum {
  FOREACH_SEGMENT_TYPE(GENERATE_ENUM_SEGMENT) SegmentTypesCount,
  SegmentTypeUnknown
} SegmentType;

/**
 * @brief Tries to convert a string to a segment type.
 *
 * The comparison is case sensitive (all string are in capital letters).
 *
 * @return
 * - Success : the given type
 * - Error : `SegmentTypeUnknown`
 */
SegmentType hl7_str2segment_type(const char *str);

/**
 * @brief Converts a segment type to a string.
 *
 * @param type
 *
 * @return constant static string
 */
const char *hl7_segment_type2str(SegmentType type);