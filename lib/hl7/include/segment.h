/**
 * @file segment.h
 * @brief HL7 "Segment" object implementation
 *
 * A segment is defined by its type and its fields.
 *
 * Segment example (MSH):
 *
 *```
 *     MSH|^~\&|FA20B02VA0234^0000000000000000^EUI-64|sdb|||20180831104332-0500||\
 *     ORU^R01^ORU_R01|{519a5093-a927-4eff-b906-1468fea313a6}|P|2.6|||AL|NE||UNICODEUTF-8|\
 * EN^English^ISO639||IHE_PCD_ORU_R01^IHEPCD^1.3.6.1.4.1.19376.1.6.4.1^ISO
 *
 *
 * Example usage:
 *
 * ```c
 *     Segment *segment = hl7_segment_from_string(str);
 *     ...
 *     const char * field = hl7_segment_get_field(0);
 *     ...
 *     hl7_segment_destruct(segment);
 *     free(segment);
 * ```
 */

#pragma once

#include "dynamic_list.h"
#include "field.h"
#include "segment_enum.h"
#include <stdbool.h>
#include <stddef.h>

// TODO: rename with prefixes

typedef struct SegmentStruct {
  SegmentType type;
  DynamicList *fields;
} Segment;

/**
 * @brief Creates a new segment.
 *
 * @return
 * - Success : pointer to the created segment
 * - Error : NULL
 */
Segment *hl7_segment_create();

/**
 * @brief Clones a segment.
 *
 * @param source Pointer to the segment to clone.
 *
 * @return
 * - Success : Pointer to the created segment
 * - Error : NULL
 */
Segment *hl7_segment_clone(const Segment *source);

/**
 * @brief Parses a string, creating a new segment
 *
 * @param str input string
 * @param separators Parent message structure holding separators values. If the
 * created segment type is MSH, this structure will be modified so next segments
 * are parsed accordingly.
 *
 * @return
 * - Success : pointer to the created segment
 * - Error : NULL
 */
Segment *hl7_segment_from_string(const char *str, Separators *separators);

/**
 * @brief Returns a string representation of the segment.
 *
 * @warning You must free the returned string.
 *
 * @param segment Pointer to the segment.
 *
 * @return
 * - Success : string representation
 * - Error : NULL
 */
char *hl7_segment_to_string(const Segment *segment,
                            const Separators *separators);

/**
 * @brief Adds a copy of a field to the segment.
 *
 * The field is appened at the end of the segment.
 * The sent field is considered owned by the segment.
 *
 * @param segment
 * @param field
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int hl7_segment_append_field(Segment *segment, Field *field);

/**
 * @brief Adds a field to the segment, represented by a string.
 *
 * The field is appened at the end of the segment.
 *
 * @param segment
 * @param field_str string representation of the field to append
 * @param separators separators used to parse the field
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int hl7_segment_append_field_string(Segment *segment, const char *field_str,
                                    const Separators *Separators);

/**
 * @brief Adds a copy of a field to the segment, by index.
 *
 * The passed field is considered owned by the segment.
 * All the fields before the index that were not yet set are initialized to
 * empty strings.
 *
 * @param segment
 * @param field
 * @param index
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int hl7_segment_set_field(Segment *segment, Field *field, size_t index);

/**
 * @brief Adds a copy of a field (represented by astring) to the segment, by
 * index.
 *
 * All the fields before the index that were not yet set are initialized to
 * empty strings.
 *
 * @param segment
 * @param field_str string representation of the field
 * @param separators separators used to parse the field
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int hl7_segment_set_field_string(Segment *segment, const char *field_str,
                                 const Separators *Separators, size_t index);

/**
 * @brief Copy a field from a segment to another.
 *
 * @param destination segment to copy to
 * @param destination_index index of the field in the destination
 * @param origin segment to copy from
 * @param origin_index index of the field in the origin
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int hl7_segment_copy_field(Segment *destination, size_t destination_index,
                           const Segment *origin, size_t origin_index);

/**
 * @brief Returns the field at a given position.
 *
 * @warning Do not modify or free the returned memory.
 *
 * @param segment Pointer to the segment.
 * @param position Index of the field (starting from 0).
 *
 * @return
 * - Success : the field (a string)
 * - Error : NULL
 */
const Field *hl7_segment_get_field(const Segment *segment, size_t position);

/**
 * @brief Returns the number of fields in the segment.
 *
 * @param segment Pointer to the segment.
 *
 * @return Number of fields in the segment.
 */
size_t hl7_segment_get_fields_count(const Segment *segment);

/**
 * @brief Sets the segment type.
 *
 * @param segment Pointer to the segment.
 * @param type Segment type.
 */
void hl7_segment_set_type(Segment *segment, const SegmentType type);

/**
 * @brief Returns the segment type.
 *
 * @param segment Pointer to the segment.
 *
 * @return Segment type.
 */
SegmentType hl7_segment_get_type(const Segment *segment);

/**
 * @brief Destroys a segment.
 *
 * @param segment Pointer to the segment to be destroyed.
 */
void hl7_segment_destruct(Segment *segment);
