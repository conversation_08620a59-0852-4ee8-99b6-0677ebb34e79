/**
 * @file field_enum.h
 * @brief HL7 field types enum.
 */

#pragma once

#include "common.h"

/// see :
/// * https://stackoverflow.com/a/10966395/13123535
/// * https://stackoverflow.com/a/16844938/13123535

// field types are not always backward-compatible

#if HL7_VERSION == 250 || HL7_VERSION == 260

#define FOREACH_FIELD_TYPE(TRANSFORM)                                          \
  TRANSFORM(AD)                                                                \
  TRANSFORM(AUI)                                                               \
  TRANSFORM(CCD)                                                               \
  TRANSFORM(CCP)                                                               \
  TRANSFORM(CD)                                                                \
  TRANSFORM(CE)                                                                \
  TRANSFORM(CF)                                                                \
  TRANSFORM(CNE)                                                               \
  TRANSFORM(CNN)                                                               \
  TRANSFORM(CP)                                                                \
  TRANSFORM(CQ)                                                                \
  TRANSFORM(CSU)                                                               \
  TRANSFORM(CWE)                                                               \
  TRANSFORM(CX)                                                                \
  TRANSFORM(DDI)                                                               \
  TRANSFORM(DIN)                                                               \
  TRANSFORM(DLD)                                                               \
  TRANSFORM(DLN)                                                               \
  TRANSFORM(DLT)                                                               \
  TRANSFORM(DR)                                                                \
  TRANSFORM(DT)                                                                \
  TRANSFORM(DTM)                                                               \
  TRANSFORM(DTN)                                                               \
  TRANSFORM(ED)                                                                \
  TRANSFORM(EI)                                                                \
  TRANSFORM(EIP)                                                               \
  TRANSFORM(ELD)                                                               \
  TRANSFORM(ERL)                                                               \
  TRANSFORM(FC)                                                                \
  TRANSFORM(FN)                                                                \
  TRANSFORM(FT)                                                                \
  TRANSFORM(GTS)                                                               \
  TRANSFORM(HD)                                                                \
  TRANSFORM(ICD)                                                               \
  TRANSFORM(ID)                                                                \
  TRANSFORM(IS)                                                                \
  TRANSFORM(JCC)                                                               \
  TRANSFORM(LA1)                                                               \
  TRANSFORM(LA2)                                                               \
  TRANSFORM(MA)                                                                \
  TRANSFORM(MO)                                                                \
  TRANSFORM(MOC)                                                               \
  TRANSFORM(MOP)                                                               \
  TRANSFORM(MSG)                                                               \
  TRANSFORM(NA)                                                                \
  TRANSFORM(NDL)                                                               \
  TRANSFORM(NM)                                                                \
  TRANSFORM(NR)                                                                \
  TRANSFORM(OCD)                                                               \
  TRANSFORM(OSD)                                                               \
  TRANSFORM(OSP)                                                               \
  TRANSFORM(PIP)                                                               \
  TRANSFORM(PL)                                                                \
  TRANSFORM(PLN)                                                               \
  TRANSFORM(PPN)                                                               \
  TRANSFORM(PRL)                                                               \
  TRANSFORM(PT)                                                                \
  TRANSFORM(PTA)                                                               \
  TRANSFORM(QIP)                                                               \
  TRANSFORM(QSC)                                                               \
  TRANSFORM(RCD)                                                               \
  TRANSFORM(RFR)                                                               \
  TRANSFORM(RI)                                                                \
  TRANSFORM(RMC)                                                               \
  TRANSFORM(RP)                                                                \
  TRANSFORM(RPT)                                                               \
  TRANSFORM(SAD)                                                               \
  TRANSFORM(SCV)                                                               \
  TRANSFORM(SI)                                                                \
  TRANSFORM(SN)                                                                \
  TRANSFORM(SPD)                                                               \
  TRANSFORM(SPS)                                                               \
  TRANSFORM(SRT)                                                               \
  TRANSFORM(ST)                                                                \
  TRANSFORM(TM)                                                                \
  TRANSFORM(TQ)                                                                \
  TRANSFORM(TS)                                                                \
  TRANSFORM(TX)                                                                \
  TRANSFORM(UVC)                                                               \
  TRANSFORM(VARIES)                                                            \
  TRANSFORM(VH)                                                                \
  TRANSFORM(VID)                                                               \
  TRANSFORM(VR)                                                                \
  TRANSFORM(WVI)                                                               \
  TRANSFORM(WVS)                                                               \
  TRANSFORM(XAD)                                                               \
  TRANSFORM(XCN)                                                               \
  TRANSFORM(XON)                                                               \
  TRANSFORM(XPN)                                                               \
  TRANSFORM(XTN)

#elif HL7_VERSION == 240

#define FOREACH_FIELD_TYPE(TRANSFORM)                                          \
  TRANSFORM(AD)                                                                \
  TRANSFORM(AUI)                                                               \
  TRANSFORM(CCD)                                                               \
  TRANSFORM(CCP)                                                               \
  TRANSFORM(CD)                                                                \
  TRANSFORM(CE)                                                                \
  TRANSFORM(CF)                                                                \
  TRANSFORM(CK)                                                                \
  TRANSFORM(CN)                                                                \
  TRANSFORM(CNE)                                                               \
  TRANSFORM(CNN)                                                               \
  TRANSFORM(CP)                                                                \
  TRANSFORM(CQ)                                                                \
  TRANSFORM(CSU)                                                               \
  TRANSFORM(CWE)                                                               \
  TRANSFORM(CX)                                                                \
  TRANSFORM(DDI)                                                               \
  TRANSFORM(DIN)                                                               \
  TRANSFORM(DLD)                                                               \
  TRANSFORM(DLN)                                                               \
  TRANSFORM(DLT)                                                               \
  TRANSFORM(DR)                                                                \
  TRANSFORM(DT)                                                                \
  TRANSFORM(DTM)                                                               \
  TRANSFORM(DTN)                                                               \
  TRANSFORM(ED)                                                                \
  TRANSFORM(EI)                                                                \
  TRANSFORM(EIP)                                                               \
  TRANSFORM(ELD)                                                               \
  TRANSFORM(FC)                                                                \
  TRANSFORM(FN)                                                                \
  TRANSFORM(FT)                                                                \
  TRANSFORM(HD)                                                                \
  TRANSFORM(ID)                                                                \
  TRANSFORM(IS)                                                                \
  TRANSFORM(JCC)                                                               \
  TRANSFORM(LA1)                                                               \
  TRANSFORM(LA2)                                                               \
  TRANSFORM(MA)                                                                \
  TRANSFORM(MO)                                                                \
  TRANSFORM(MOC)                                                               \
  TRANSFORM(MOP)                                                               \
  TRANSFORM(MSG)                                                               \
  TRANSFORM(NA)                                                                \
  TRANSFORM(NDL)                                                               \
  TRANSFORM(NM)                                                                \
  TRANSFORM(NR)                                                                \
  TRANSFORM(OCD)                                                               \
  TRANSFORM(OSD)                                                               \
  TRANSFORM(OSP)                                                               \
  TRANSFORM(PCF)                                                               \
  TRANSFORM(PI)                                                                \
  TRANSFORM(PIP)                                                               \
  TRANSFORM(PL)                                                                \
  TRANSFORM(PLN)                                                               \
  TRANSFORM(PN)                                                                \
  TRANSFORM(PPN)                                                               \
  TRANSFORM(PRL)                                                               \
  TRANSFORM(PT)                                                                \
  TRANSFORM(PTA)                                                               \
  TRANSFORM(QIP)                                                               \
  TRANSFORM(QSC)                                                               \
  TRANSFORM(RCD)                                                               \
  TRANSFORM(RFR)                                                               \
  TRANSFORM(RI)                                                                \
  TRANSFORM(RMC)                                                               \
  TRANSFORM(RP)                                                                \
  TRANSFORM(SAD)                                                               \
  TRANSFORM(SCV)                                                               \
  TRANSFORM(SI)                                                                \
  TRANSFORM(SN)                                                                \
  TRANSFORM(SPD)                                                               \
  TRANSFORM(SPS)                                                               \
  TRANSFORM(SRT)                                                               \
  TRANSFORM(ST)                                                                \
  TRANSFORM(TM)                                                                \
  TRANSFORM(TN)                                                                \
  TRANSFORM(TQ)                                                                \
  TRANSFORM(TS)                                                                \
  TRANSFORM(TX)                                                                \
  TRANSFORM(UVC)                                                               \
  TRANSFORM(VARIES)                                                            \
  TRANSFORM(VH)                                                                \
  TRANSFORM(VID)                                                               \
  TRANSFORM(VR)                                                                \
  TRANSFORM(WVI)                                                               \
  TRANSFORM(WVS)                                                               \
  TRANSFORM(XAD)                                                               \
  TRANSFORM(XCN)                                                               \
  TRANSFORM(XON)                                                               \
  TRANSFORM(XPN)                                                               \
  TRANSFORM(XTN)
#else
#error "unhandled HL7 version"
#endif

#define GENERATE_ENUM_FIELD(ENUM) FieldType##ENUM,

/**
 * @brief Enum for all field types in hl7.
 */
typedef enum FieldTypeStruct {
  FOREACH_FIELD_TYPE(GENERATE_ENUM_FIELD) FieldTypesCount,
  FieldTypeUnknown
} FieldType;

/**
 * @brief Tries to convert a string to a field type.
 *
 * The comparison is case sensitive (all string are in capital letters).
 *
 * @return
 * - Success : the given type
 * - Error : `SegmentTypeUnknown`
 */
FieldType hl7_str2field_type(const char *str);

/**
 * @brief Converts a field type to a string.
 *
 * @param type
 *
 * @return constant static string
 */
const char *hl7_field_type2str(FieldType type);