/**
 * @file specification.h
 * @brief Module for specifying segments and fields.
 */

#pragma once

#include "field_enum.h"
#include "message.h"
#include "segment_enum.h"
#include "table_enum.h"
#include <stdbool.h>

enum { HL7_VALID, HL7_INVALID, HL7_ERROR };

enum { REQUIRED = false, OPTIONAL = true };

#define NOT_REPEATABLE 1
#define REPEATABLE_INFINITE -1
#define TABLE_NONE TableTypeUnknown

typedef struct SubFieldSpecStruct {
  const FieldType type;
  const size_t max_length;
  const bool optional;
  const TableType table;
} SubFieldSpec;

typedef SubFieldSpec ComposedFieldSpec[];

/**
 * @brief Field specification when in a segment.
 */
typedef struct FieldInSegmentSpecStruct {
  const char *name;
  FieldType type;
  /**
   * -1 for no size limit
   */
  int max_length;
  /**
   * -1 = infinitly repeatable
   * other values are positive upper bound
   * 0 is an invalid value
   */
  bool optional;
  int repeatability;
  // optional table type if the field takes its value in a table
  TableType table_type;
} FieldInSegmentSpec;

typedef FieldInSegmentSpec SegmentSpecification[];

typedef struct {
  const char *table_name;
  const char **valid_values;
  size_t value_count;
} HL7Table;

typedef struct HL7ParsingErrorPositionStruct {
  SegmentType segment_type;
  size_t segment_index;
  const SegmentSpecification *segment_spec;
  FieldType field_type;
  size_t field_index;
  size_t character_index; // index in segment representation
} HL7ParsingErrorPosition;

HL7ParsingErrorPosition *
hl7_position_clone(const HL7ParsingErrorPosition *position);

typedef struct HL7ParsingErrorStruct {
  HL7ParsingErrorPosition *position;
  char *message;
} HL7ParsingError;

typedef struct HL7ParsingErrorsHelperStruct {
  DynamicList *errors;
  HL7ParsingErrorPosition current_position;
  const Message *message;
} HL7ParsingErrorsHelper;

HL7ParsingErrorsHelper *hl7_errors_helper_create(const Message *message);

int hl7_errors_helper_add(HL7ParsingErrorsHelper *helper, const char *format,
                          ...);

char *hl7_errors2str(const HL7ParsingErrorsHelper *helper);

void hl7_errors_helper_destruct(HL7ParsingErrorsHelper *helper);
