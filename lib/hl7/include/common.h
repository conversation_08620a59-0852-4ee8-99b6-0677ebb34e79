/**
 * @file common.h
 * @brief Common tools for the hl7 library.
 */

#pragma once

#include "dynamic_list.h"

#ifndef HL7_VERSION
#define HL7_VERSION 260
#endif

#define FIELD_SEPARATOR_DEFAULT '|'
#define COMPONENT_SEPARATOR_DEFAULT '^'
#define REPETITION_SEPARATOR_DEFAULT '~'
#define ESCAPE_SEPARATOR_DEFAULT '\\'
#define SUBCOMPONENT_SEPARATOR_DEFAULT '&'

typedef struct SeparatorsStruct {
  char field_separator;
  char component_separator;
  char repetition_separator;
  char escape_character;
  char subcomponent_separator;
} Separators;
