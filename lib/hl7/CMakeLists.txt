file(
  GLOB_RECURSE
  SOURCES
  "${CMAKE_CURRENT_SOURCE_DIR}/src/*.c"
  "${CMAKE_CURRENT_SOURCE_DIR}/src/field_specifications/*.c"
  "${CMAKE_CURRENT_SOURCE_DIR}/src/segment_specifications/*.c"
  "${CMAKE_CURRENT_SOURCE_DIR}/src/table_specifications/*.c")

# this way of defining versions is ugly, but I've found no other working way

add_library(hl7_240 STATIC ${SOURCES})
target_compile_definitions(hl7_240 PRIVATE HL7_VERSION=240)
target_include_directories(
  hl7_240 PUBLIC include include/field_specifications
                 include/segments_specifications include/table_specifications)
target_link_libraries(hl7_240 dynamic_list log)

add_library(hl7_250 STATIC ${SOURCES})
target_compile_definitions(hl7_250 PRIVATE HL7_VERSION=250)
target_include_directories(
  hl7_250 PUBLIC include include/field_specifications
                 include/segments_specifications include/table_specifications)
target_link_libraries(hl7_250 dynamic_list log)

add_library(hl7_260 STATIC ${SOURCES})
target_compile_definitions(hl7_260 PRIVATE HL7_VERSION=260)
target_include_directories(
  hl7_260 PUBLIC include include/field_specifications
                 include/segments_specifications include/table_specifications)
target_link_libraries(hl7_260 dynamic_list log)
