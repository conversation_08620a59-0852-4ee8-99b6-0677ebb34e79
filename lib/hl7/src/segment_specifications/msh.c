#include "msh.h"

#if HL7_VERSION == 260

// clang-format off
const FieldInSegmentSpec MSH_Spec[25] = {
    {"field separator", FieldTypeST, 1, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"encoding characters", FieldTypeST, 4, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"sending application", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"sending facility", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"receiving application", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"receiving facility", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"date/time of message", FieldTypeDTM, 24, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"security", FieldTypeST, 40, OP<PERSON>ONAL, NOT_REPEATABLE, TAB<PERSON>_NONE},
    {"message type", FieldTypeMSG, 15, <PERSON><PERSON><PERSON><PERSON>RE<PERSON>, NOT_REPEATABLE, TAB<PERSON>_NONE},
    {"message control ID", FieldTypeST, 199, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"processing ID", FieldTypePT, 3, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"version ID", FieldTypeVID, 60, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"sequence number", FieldTypeNM, 15, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"continuation pointer", FieldTypeST, 180, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"accept acknowledgment type", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0155},
    {"application acknowledgment type", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0155},
    {"country code", FieldTypeID, 3, OPTIONAL, NOT_REPEATABLE, TableType0399},
    {"character set", FieldTypeID, 16, OPTIONAL, REPEATABLE_INFINITE, TableType0211},
    {"principal language of message", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"alternate character set handling scheme", FieldTypeID, 20, OPTIONAL, NOT_REPEATABLE, TableType0356},
    {"message profile identifier", FieldTypeEI, 427, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"sending responsible organization", FieldTypeXON, 567, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"receiving responsible organization", FieldTypeXON, 567, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"sending network address", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"receiving network address", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
};
// clang-format on

#elif HL7_VERSION == 250

// clang-format off
const FieldInSegmentSpec MSH_Spec[21] = {
    {"field separator", FieldTypeST, 1, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"encoding characters", FieldTypeST, 4, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"sending application", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TableType0361},
    {"sending facility", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TableType0362},
    {"receiving application", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TableType0361},
    {"receiving facility", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TableType0362},
    {"date/time of message", FieldTypeDTM, 24, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"security", FieldTypeST, 40, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"message type", FieldTypeMSG, 15, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"message control ID", FieldTypeST, 199, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"processing ID", FieldTypePT, 3, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"version ID", FieldTypeVID, 60, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"sequence number", FieldTypeNM, 15, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"continuation pointer", FieldTypeST, 180, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"accept acknowledgment type", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0155},
    {"application acknowledgment type", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0155},
    {"country code", FieldTypeID, 3, OPTIONAL, NOT_REPEATABLE, TableType0399},
    {"character set", FieldTypeID, 16, OPTIONAL, REPEATABLE_INFINITE, TableType0211},
    {"principal language of message", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"alternate character set handling scheme", FieldTypeID, 20, OPTIONAL, NOT_REPEATABLE, TableType0356},
    {"message profile identifier", FieldTypeEI, 427, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
};
// clang-format on

#elif HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec MSH_Spec[21] = {
    {"field separator", FieldTypeST, 1, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"encoding characters", FieldTypeST, 4, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"sending application", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TableType0361},
    {"sending facility", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TableType0362},
    {"receiving application", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TableType0361},
    {"receiving facility", FieldTypeHD, 227, OPTIONAL, NOT_REPEATABLE, TableType0362},
    {"date/time of message", FieldTypeDTM, 24, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"security", FieldTypeST, 40, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"message type", FieldTypeMSG, 15, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"message control ID", FieldTypeST, 199, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"processing ID", FieldTypePT, 3, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"version ID", FieldTypeVID, 60, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"sequence number", FieldTypeNM, 15, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"continuation pointer", FieldTypeST, 180, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"accept acknowledgment type", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0155},
    {"application acknowledgment type", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0155},
    {"country code", FieldTypeID, 3, OPTIONAL, NOT_REPEATABLE, TableType0399},
    {"character set", FieldTypeID, 16, OPTIONAL, REPEATABLE_INFINITE, TableType0211},
    {"principal language of message", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"alternate character set handling scheme", FieldTypeID, 20, OPTIONAL, NOT_REPEATABLE, TableType0356},
    {"conformance statement ID", FieldTypeID, 10, OPTIONAL, REPEATABLE_INFINITE, TableType0449},
};
// clang-format on

#endif