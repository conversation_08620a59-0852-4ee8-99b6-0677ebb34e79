#include "equ.h"

#if HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec EQU_Spec[5] = {
    {"equipment instance identifier", FieldTypeEI, 22, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"event date/time", FieldTypeTS, 26, <PERSON><PERSON><PERSON><PERSON>RE<PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"equipment state", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0365},
    {"local/remote control state", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0366},
    {"alert level", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0367},
};
// clang-format on

#endif