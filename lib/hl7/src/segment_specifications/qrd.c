#include "qrd.h"

#if HL7_VERSION == 260

// clang-format off
const FieldInSegmentSpec QRD_Spec[12] = {
    {"query date/time", FieldTypeDTM, 24, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"query format code", FieldTypeID, 1, R<PERSON><PERSON><PERSON>RE<PERSON>, NOT_REPEATABLE, TableType0106},
    {"query priority", FieldTypeID, 1, REQUIRED, NOT_REPEATABLE, TableType0091},
    {"query ID", FieldTypeST, 32, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"deferred response type", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0107},
    {"deferred response date/time", FieldTypeDTM, 24, OP<PERSON>ONAL, NOT_REPEATABLE, TABLE_NONE},
    {"quantity limited request", FieldTypeCQ, 10, REQUIRE<PERSON>, NOT_REPEATABLE, TableType0126},
    {"who subject filter", FieldTypeXCN, 250, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>TABLE_INFINITE, TABLE_NONE},
    {"what subject filter", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"what department data code", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"what data code value qual.", FieldTypeVR, 20, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"query results level", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0108},
};
// clang-format on

#elif HL7_VERSION == 250

// clang-format off
const FieldInSegmentSpec QRD_Spec[12] = {
    {"query date/time", FieldTypeDTM, 24, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"query format code", FieldTypeID, 1, REQUIRED, NOT_REPEATABLE, TableType0106},
    {"query priority", FieldTypeID, 1, REQUIRED, NOT_REPEATABLE, TableType0091},
    {"query ID", FieldTypeST, 32, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"deferred response type", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0107},
    {"deferred response date/time", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"quantity limited request", FieldTypeCQ, 10, REQUIRED, NOT_REPEATABLE, TableType0126},
    {"who subject filter", FieldTypeXCN, 250, REQUIRED, REPEATABLE_INFINITE, TABLE_NONE},
    {"what subject filter", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"what department data code", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"what data code value qual.", FieldTypeVR, 20, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"query results level", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0108},
};
// clang-format on

#elif HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec QRD_Spec[10] = {
    {"query date/time", FieldTypeTS, 26, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"query format code", FieldTypeID, 1, REQUIRED, NOT_REPEATABLE, TableType0106},
    {"query priority", FieldTypeID, 1, REQUIRED, NOT_REPEATABLE, TableType0091},
    {"query ID", FieldTypeST, 32, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"deferred response type", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0107},
    {"deferred response date/time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"quantity limited request", FieldTypeCQ, 10, REQUIRED, NOT_REPEATABLE, TableType0126},
    {"who subject filter", FieldTypeXCN, 250, REQUIRED, REPEATABLE_INFINITE, TABLE_NONE},
    {"what subject filter", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"what department data code", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
};
// clang-format on

#endif