#include "obr.h"

#if HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec OBR_Spec[47] = {
    {"set ID", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"placer order number", FieldTypeEI, 22, OP<PERSON><PERSON><PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"filler order number", FieldTypeEI, 22, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"universal service identifier", FieldTypeCE, 250, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"priority", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"requested date/time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation Date/Time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation End Date/Time", FieldTypeTS, 26, O<PERSON><PERSON>ON<PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"collection Volume *", FieldTypeCQ, 20, OP<PERSON>ONAL, NOT_REPEATABLE, TABLE_NONE},
    {"collector Identifier *", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"specimen Action Code *", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0065},
    {"danger Code", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"relevant Clinical Info", FieldTypeST, 300, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"specimen Received Date/Time *", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"specimen Source", FieldTypeSPS, 300, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"ordering Provider", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"order Callback Phone Number", FieldTypeXTN, 250, OPTIONAL, 2, TABLE_NONE},
    {"placer Field 1", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"placer Field 2", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler Field 1 +", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler Field 2 +", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"results Rpt/Status Chng - Date/Time +", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"charge to Practice +", FieldTypeMOC, 40, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"diagnostic Serv Sect ID", FieldTypeID, 10, OPTIONAL, NOT_REPEATABLE, TableType0074},
    {"result Status +", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0123},
    {"parent Result +", FieldTypePRL, 400, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"quantity/Timing", FieldTypeTQ, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"result Copies To", FieldTypeXCN, 250, OPTIONAL, 5, TABLE_NONE},
    {"parent Number", FieldTypeEIP, 200, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transportation Mode", FieldTypeID, 20, OPTIONAL, NOT_REPEATABLE, TableType0124},
    {"reason for Study", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"principal Result Interpreter +", FieldTypeNDL, 200, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"assistant Result Interpreter +", FieldTypeNDL, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"technician +", FieldTypeNDL, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"transcriptionist +", FieldTypeNDL, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"scheduled Date/Time +", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"number of Sample Containers *", FieldTypeNM, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transport Logistics of Collected Sample *", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"collector's Comment *", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"transport Arrangement Responsibility", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transport Arranged", FieldTypeID, 30, OPTIONAL, NOT_REPEATABLE, TableType0224},
    {"escort Required", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0225},
    {"planned Patient Transport Comment", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"procedure Code", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0088},
    {"procedure Code Modifier", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0340},
    {"placer Supplemental Service Information", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0411},
    {"filler Supplemental Service Information", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0411},
};
// clang-format on

#elif HL7_VERSION == 250

// clang-format off
const FieldInSegmentSpec OBR_Spec[49] = {
    {"set ID", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"placer order number", FieldTypeEI, 22, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler order number", FieldTypeEI, 22, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"universal service identifier", FieldTypeCE, 250, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"priority", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"requested date/time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation Date/Time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation End Date/Time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"collection Volume *", FieldTypeCQ, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"collector Identifier *", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"specimen Action Code *", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0065},
    {"danger Code", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"relevant Clinical Info", FieldTypeST, 300, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"specimen Received Date/Time *", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"specimen Source", FieldTypeSPS, 300, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"ordering Provider", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"order Callback Phone Number", FieldTypeXTN, 250, OPTIONAL, 2, TABLE_NONE},
    {"placer Field 1", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"placer Field 2", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler Field 1 +", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler Field 2 +", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"results Rpt/Status Chng - Date/Time +", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"charge to Practice +", FieldTypeMOC, 40, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"diagnostic Serv Sect ID", FieldTypeID, 10, OPTIONAL, NOT_REPEATABLE, TableType0074},
    {"result Status +", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0123},
    {"parent Result +", FieldTypePRL, 400, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"quantity/Timing", FieldTypeTQ, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"result Copies To", FieldTypeXCN, 250, OPTIONAL, 5, TABLE_NONE},
    {"parent Number", FieldTypeEIP, 200, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transportation Mode", FieldTypeID, 20, OPTIONAL, NOT_REPEATABLE, TableType0124},
    {"reason for Study", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"principal Result Interpreter +", FieldTypeNDL, 200, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"assistant Result Interpreter +", FieldTypeNDL, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"technician +", FieldTypeNDL, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"transcriptionist +", FieldTypeNDL, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"scheduled Date/Time +", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"number of Sample Containers *", FieldTypeNM, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transport Logistics of Collected Sample *", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"collector's Comment *", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"transport Arrangement Responsibility", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transport Arranged", FieldTypeID, 30, OPTIONAL, NOT_REPEATABLE, TableType0224},
    {"escort Required", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0225},
    {"planned Patient Transport Comment", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"procedure Code", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0088},
    {"procedure Code Modifier", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0340},
    {"placer Supplemental Service Information", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0411},
    {"filler Supplemental Service Information", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0411},
    {"medically Necessary Duplicate Procedure Reason.", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TableType0476},
    {"result Handling", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0507},
};
// clang-format on

#elif HL7_VERSION == 260

// clang-format off
const FieldInSegmentSpec OBR_Spec[50] = {
    {"set ID", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"placer order number", FieldTypeEI, 427, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler order number", FieldTypeEI, 427, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"universal service identifier", FieldTypeCWE, 705, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"priority", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"requested date/time", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation Date/Time", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation End Date/Time", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"collection Volume *", FieldTypeCQ, 722, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"collector Identifier *", FieldTypeXCN, 3220, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"specimen Action Code *", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0065},
    {"danger Code", FieldTypeCWE, 705, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"relevant Clinical Info", FieldTypeST, 300, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"specimen Received Date/Time *", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"specimen Source", FieldTypeSPS, 300, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"ordering Provider", FieldTypeXCN, 3220, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"order Callback Phone Number", FieldTypeXTN, 2743, OPTIONAL, 2, TABLE_NONE},
    {"placer Field 1", FieldTypeST, 199, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"placer Field 2", FieldTypeST, 199, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler Field 1 +", FieldTypeST, 199, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler Field 2 +", FieldTypeST, 199, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"results Rpt/Status Chng - Date/Time +", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"charge to Practice +", FieldTypeMOC, 40, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"diagnostic Serv Sect ID", FieldTypeID, 10, OPTIONAL, NOT_REPEATABLE, TableType0074},
    {"result Status +", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0123},
    {"parent Result +", FieldTypePRL, 400, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"quantity/Timing", FieldTypeTQ, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"result Copies To", FieldTypeXCN, 3220, OPTIONAL, 5, TABLE_NONE},
    {"parent Number", FieldTypeEIP, 855, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transportation Mode", FieldTypeID, 20, OPTIONAL, NOT_REPEATABLE, TableType0124},
    {"reason for Study", FieldTypeCWE, 705, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"principal Result Interpreter +", FieldTypeNDL, 831, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"assistant Result Interpreter +", FieldTypeNDL, 831, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"technician +", FieldTypeNDL, 831, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"transcriptionist +", FieldTypeNDL, 831, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"scheduled Date/Time +", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"number of Sample Containers *", FieldTypeNM, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transport Logistics of Collected Sample *", FieldTypeCWE, 705, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"collector's Comment *", FieldTypeCWE, 705, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"transport Arrangement Responsibility", FieldTypeCWE, 705, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transport Arranged", FieldTypeID, 30, OPTIONAL, NOT_REPEATABLE, TableType0224},
    {"escort Required", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0225},
    {"planned Patient Transport Comment", FieldTypeCWE, 705, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"procedure Code", FieldTypeCNE, 705, OPTIONAL, NOT_REPEATABLE, TableType0088},
    {"procedure Code Modifier", FieldTypeCNE, 705, OPTIONAL, REPEATABLE_INFINITE, TableType0340},
    {"placer Supplemental Service Information", FieldTypeCWE, 705, OPTIONAL, REPEATABLE_INFINITE, TableType0411},
    {"filler Supplemental Service Information", FieldTypeCWE, 705, OPTIONAL, REPEATABLE_INFINITE, TableType0411},
    {"medically Necessary Duplicate Procedure Reason.", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TableType0476},
    {"result Handling", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0507},
    {"parent Universal Service Identifier", FieldTypeCWE, 705, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
};
// clang-format on

#endif