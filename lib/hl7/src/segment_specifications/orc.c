#include "equ.h"

#if HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec ORC_Spec[25] = {
    {"order control", FieldTypeID, 2, REQUIRED, NOT_REPEATABLE, TableType0119},
    {"place order nmber", FieldTypeEI, 22, OP<PERSON><PERSON><PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"filler order number", FieldTypeEI, 22, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"placer group number", FieldTypeEI, 22, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"order status", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0038},
    {"response flag", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0121},
    {"quantity/timing", FieldTypeTQ, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"parent order", FieldTypeEIP, 200, OPTIONAL, NOT_REPEATABLE, TAB<PERSON>_NONE},
    {"date/time of transaction", FieldTypeTS, 26, <PERSON><PERSON><PERSON><PERSON><PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"entered by", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"verified by", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ordering provider", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"enterer's location", FieldTypePL, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"call back phone number", FieldTypeXTN, 250, OPTIONAL, 2, TABLE_NONE},
    {"order effective date/time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"order control code reason", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"entering organization", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"entering device", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"action by", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"advanced beneficiary notice code", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0339},
    {"ordering facility name", FieldTypeXON, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ordering facility address", FieldTypeXAD, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ordering facility phone number", FieldTypeXTN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ordering provider access", FieldTypeXAD, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"order status modifier", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
};
// clang-format on

#elif HL7_VERSION == 250

// clang-format off
const FieldInSegmentSpec ORC_Spec[30] = {
    {"order control", FieldTypeID, 2, REQUIRED, NOT_REPEATABLE, TableType0119},
    {"place order nmber", FieldTypeEI, 22, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"filler order number", FieldTypeEI, 22, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"placer group number", FieldTypeEI, 22, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"order status", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0038},
    {"response flag", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0121},
    {"quantity/timing", FieldTypeTQ, 200, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"parent order", FieldTypeEIP, 200, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"date/time of transaction", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"entered by", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"verified by", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ordering provider", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"enterer's location", FieldTypePL, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"call back phone number", FieldTypeXTN, 250, OPTIONAL, 2, TABLE_NONE},
    {"order effective date/time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"order control code reason", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"entering organization", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"entering device", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"action by", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"advanced beneficiary notice code", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0339},
    {"ordering facility name", FieldTypeXON, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ordering facility address", FieldTypeXAD, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ordering facility phone number", FieldTypeXTN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ordering provider access", FieldTypeXAD, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"order status modifier", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"advanced beneficiary notice override reason", FieldTypeCWE, 60, OPTIONAL, NOT_REPEATABLE, TableType0552},
    {"filler's expected availability date/time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"confidentiality code", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TableType0177},
    {"order type", FieldTypeCWE, 250, OPTIONAL, NOT_REPEATABLE, TableType0482},
    {"enterer athorization code", FieldTypeCNE, 250, OPTIONAL, NOT_REPEATABLE, TableType0483},
};
// clang-format on

#endif
