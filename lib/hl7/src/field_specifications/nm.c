#include "nm.h"

#if HL7_VERSION == 240 || HL7_VERSION == 250 || HL7_VERSION == 260

#include <ctype.h>

void validate_NM(const char *field, HL7ParsingErrorsHelper *helper) {
  if (!field || *field == '\0')
    hl7_errors_helper_add(helper, "field is null or empty string");

  const char *ptr = field;

  // Check for optional leading sign (+ or -)
  if (*ptr == '+' || *ptr == '-')
    ptr++;

  bool has_digits = false;
  bool has_decimal = false;

  // Traverse through the digits
  while (*ptr) {
    if (isdigit(*ptr)) {
      has_digits = true; // Found at least one digit
    } else if (*ptr == '.') {
      // Decimal point should only appear once
      if (has_decimal) {
        hl7_errors_helper_add(helper, "not a number : %s (found multilpe dots)",
                              field);
        return;
      }
      has_decimal = true;
    } else {
      hl7_errors_helper_add(
          helper, "not a number : %s (non numerical character)", field);
      return;
    }
    ptr++;
  }

  if (!has_digits)
    hl7_errors_helper_add(
        helper, "not a number : %s (must contain at least one digit)", field);
}

#endif