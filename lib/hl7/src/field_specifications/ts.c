#include "ts.h"

#if HL7_VERSION == 240

const SubFieldSpec TS_SPEC[2] = {
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
};

#elif HL7_VERSION == 250

const SubFieldSpec TS_SPEC[2] = {
    {FieldTypeDTM, 24, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeID, 1, OPTION<PERSON>, TableType0259},
};

#elif HL7_VERSION == 250 || HL7_VERSION == 260

const SubFieldSpec TS_SPEC[2] = {
    {FieldTypeDTM, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 0, OPTIONAL, TABLE_NONE},
};

#endif
