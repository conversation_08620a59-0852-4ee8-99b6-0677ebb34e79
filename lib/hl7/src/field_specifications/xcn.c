#include "xcn.h"

#if HL7_VERSION == 240

const SubFieldSpec XCN_SPEC[18] = {
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeFN, 0, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeST, 0, <PERSON>P<PERSON><PERSON><PERSON>, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 0, OPTION<PERSON>, TABLE_NONE},
    {FieldTypeIS, 0, OPTIONAL, TableType0360},
    {FieldTypeIS, 0, OPTIONAL, TableType0297},
    {FieldTypeHD, 0, OPTION<PERSON>, TABLE_NONE},
    {FieldTypeID, 0, OPTIONAL, TableType0200},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 0, OPTIONAL, TableType0061},
    {FieldTypeIS, 0, OP<PERSON>ON<PERSON>, TableType0203},
    {FieldTypeHD, 0, OP<PERSON><PERSON><PERSON>, TAB<PERSON>_NONE},
    {FieldTypeID, 0, OP<PERSON><PERSON><PERSON>, TableType0465},
    {FieldTypeCE, 0, OP<PERSON>ONAL, TableType0448},
    {FieldTypeDR, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 0, OPTIONAL, TableType0444},
};

#elif HL7_VERSION == 250

const SubFieldSpec XCN_SPEC[23] = {
    {FieldTypeST, 15, OPTIONAL, TABLE_NONE},
    {FieldTypeFN, 194, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 30, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 30, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeIS, 5, OPTIONAL, TableType0360},
    {FieldTypeIS, 4, OPTIONAL, TableType0297},
    {FieldTypeHD, 227, OPTIONAL, TableType0363},
    {FieldTypeID, 1, OPTIONAL, TableType0200},
    {FieldTypeST, 1, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 3, OPTIONAL, TableType0061},
    {FieldTypeID, 5, OPTIONAL, TableType0203},
    {FieldTypeHD, 227, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 1, OPTIONAL, TableType0465},
    {FieldTypeCE, 483, OPTIONAL, TableType0448},
    {FieldTypeDR, 53, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 1, OPTIONAL, TableType0444},
    {FieldTypeTS, 26, OPTIONAL, TABLE_NONE},
    {FieldTypeTS, 26, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OPTIONAL, TABLE_NONE},
};

#elif HL7_VERSION == 260

const SubFieldSpec XCN_SPEC[23] = {
    {FieldTypeST, 15, OPTIONAL, TABLE_NONE},
    {FieldTypeFN, 194, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 30, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 30, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeIS, 5, OPTIONAL, TableType0360},
    {FieldTypeIS, 4, OPTIONAL, TableType0297},
    {FieldTypeHD, 227, OPTIONAL, TableType0363},
    {FieldTypeID, 1, OPTIONAL, TableType0200},
    {FieldTypeST, 1, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 3, OPTIONAL, TableType0061},
    {FieldTypeID, 5, OPTIONAL, TableType0203},
    {FieldTypeHD, 227, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 1, OPTIONAL, TableType0465},
    {FieldTypeCE, 483, OPTIONAL, TableType0448},
    {FieldTypeDR, 53, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 1, OPTIONAL, TableType0444},
    {FieldTypeTS, 26, OPTIONAL, TABLE_NONE},
    {FieldTypeTS, 26, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OPTIONAL, TABLE_NONE},
};

#endif
