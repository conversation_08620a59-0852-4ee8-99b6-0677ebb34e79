#include "xpn.h"

#if HL7_VERSION == 240

const SubFieldSpec XPN_SPEC[11] = {
    {FieldTypeFN, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 0, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeST, 0, <PERSON>P<PERSON><PERSON><PERSON>, TABLE_NONE},
    {FieldTypeST, 0, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeIS, 0, OPTIONAL, TableType0360},
    {FieldTypeID, 0, OPTIONAL, TableType0200},
    {FieldTypeID, 0, OPTIONAL, TableType0465},
    {FieldTypeCE, 0, OPTIONAL, TableType0448},
    {FieldTypeDR, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 0, OPTIONAL, TableType0444},
};

#elif HL7_VERSION == 250

const SubFieldSpec XPN_SPEC[14] = {
    {FieldTypeFN, 194, <PERSON><PERSON><PERSON><PERSON><PERSON>, TABLE_NONE},
    {FieldTypeST, 30, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_NONE},
    {FieldTypeST, 30, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ABLE_NONE},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeIS, 6, OPTIONAL, TableType0360},
    {FieldTypeID, 1, OPTIONAL, TableType0200},
    {FieldTypeID, 1, OPTIONAL, TableType0465},
    {FieldTypeCE, 483, OPTIONAL, TableType0448},
    {FieldTypeDR, 49, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 1, OPTIONAL, TableType0444},
    {FieldTypeDTM, 24, OPTIONAL, TABLE_NONE},
    {FieldTypeDTN, 24, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
};

#elif HL7_VERSION == 260

const SubFieldSpec XPN_SPEC[14] = {
    {FieldTypeFN, 194, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 30, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 30, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeIS, 6, OPTIONAL, TableType0360},
    {FieldTypeID, 1, OPTIONAL, TableType0200},
    {FieldTypeID, 1, OPTIONAL, TableType0465},
    {FieldTypeCWE, 705, OPTIONAL, TableType0448},
    {FieldTypeDR, 49, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 1, OPTIONAL, TableType0444},
    {FieldTypeDTM, 24, OPTIONAL, TABLE_NONE},
    {FieldTypeDTN, 24, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
};

#endif
