#include "dtm.h"
#include "specification.h"
#include <ctype.h>
#include <stddef.h>
#include <stdlib.h>

int is_digit_str(const char *str, size_t len, HL7ParsingErrorsHelper *helper) {
  for (size_t i = 0; i < len; ++i)
    if (!isdigit(str[i])) {
      hl7_errors_helper_add(helper, "date should only contain digits");
      return 0;
    }

  return 1;
}

void validate_DTM(const char *field, HL7ParsingErrorsHelper *helper) {
  size_t len = strlen(field);
  char tmp[20];

  // Minimum length for YYYY (4 digits)
  if (len < 4) {
    hl7_errors_helper_add(helper, "date should be at least 4 characters");
    return;
  }

  if (!is_digit_str(field, 4, helper))
    return;

  // Parsing YYYY
  memcpy(tmp, field, 4);
  tmp[4] = '\0';
  int year = atoi(tmp);
  if (year < 1000 || year > 9999) {
    hl7_errors_helper_add(helper, "invalid year : %d", year);
    return;
  }

  // Optional MM
  if (len >= 6) {
    if (!is_digit_str(field + 4, 2, helper))
      return;

    memcpy(tmp, field + 4, 2);
    tmp[2] = '\0';
    int month = atoi(tmp);
    if (month < 1 || month > 12) {
      hl7_errors_helper_add(helper, "month outside of range [1,12]");
      return;
    }
  }

  // Optional DD
  if (len >= 8) {
    if (!is_digit_str(field + 6, 2, helper))
      return;

    memcpy(tmp, field + 6, 2);
    tmp[2] = '\0';
    int day = atoi(tmp);
    if (day < 1 || day > 31) {
      hl7_errors_helper_add(helper, "day outside of range [1,31]");
      return;
    }
  }

  // Optional HH
  if (len >= 10) {
    if (!is_digit_str(field + 8, 2, helper))
      return;

    memcpy(tmp, field + 8, 2);
    tmp[2] = '\0';
    int hour = atoi(tmp);
    if (hour < 0 || hour > 23) {
      hl7_errors_helper_add(helper, "hour outside of range [0,23]");
      return;
    }
  }

  // Optional MM
  if (len >= 12) {
    if (!is_digit_str(field + 10, 2, helper))
      return;

    memcpy(tmp, field + 10, 2);
    tmp[2] = '\0';
    int minute = atoi(tmp);
    if (minute < 0 || minute > 59) {
      hl7_errors_helper_add(helper, "minute outside of range [0,59]");
      return;
    }
  }

  // Optional SS
  if (len >= 14) {
    if (!is_digit_str(field + 12, 2, helper))
      return;

    memcpy(tmp, field + 12, 2);
    tmp[2] = '\0';
    int second = atoi(tmp);
    if (second < 0 || second > 59) {
      hl7_errors_helper_add(helper, "second out of range [0,59]");
      return;
    }
  }

  // Optional .S[S[S[S]]]
  if (len >= 16 && field[14] == '.') {
    size_t fractional_length = 0;
    while (isdigit(field[15 + fractional_length]))
      fractional_length++;
    if (fractional_length == 0 || fractional_length > 4) {
      hl7_errors_helper_add(
          helper, "fractionnal length should be between 1 and 3 characters");
      return;
    }
  }

  // Optional +/-ZZZZ (Timezone)
  if (len > 14 && (field[len - 5] == '+' || field[len - 5] == '-')) {
    if (!is_digit_str(field + len - 4, 4, helper))
      return;

    memcpy(tmp, field + len - 4, 4);
    tmp[4] = '\0';
    int timezone_offset = atoi(tmp);
    if (timezone_offset < -1200 || timezone_offset > 1200) {
      hl7_errors_helper_add(helper,
                            "timezone offset outside of range [-1200, 1200]");
      return;
    }
  }
}
