#include "cwe.h"

#if HL7_VERSION == 240

const SubFieldSpec CWE_SPEC[9] = {
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 0, OPTION<PERSON>, TABLE_NONE},
    {FieldTypeID, 0, <PERSON><PERSON><PERSON><PERSON><PERSON>, TableType0396},
    {FieldTypeST, 0, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 0, OPTIONAL, TableType0396},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 0, OPTION<PERSON>, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
};

#elif HL7_VERSION == 250 || HL7_VERSION == 260

const SubFieldSpec CWE_SPEC[9] = {
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, O<PERSON><PERSON><PERSON><PERSON>, TABLE_NONE},
    {FieldTypeID, 20, O<PERSON><PERSON><PERSON><PERSON>, TableType0396},
    {FieldTypeST, 20, O<PERSON><PERSON><PERSON><PERSON>, TABLE_NONE},
    {FieldTypeST, 199, OP<PERSON>ONAL, TABLE_NONE},
    {FieldTypeID, 20, OPTIONAL, TableType0396},
    {FieldTypeST, 10, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 10, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
};

#endif
