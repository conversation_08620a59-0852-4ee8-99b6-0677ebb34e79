#include "ce.h"

#if HL7_VERSION == 240

const SubFieldSpec CE_SPEC[6] = {
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
    {FieldTypeIS, 20, <PERSON><PERSON><PERSON><PERSON><PERSON>, TableType0396},
    {FieldTypeST, 20, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
    {FieldTypeIS, 20, OPTIONAL, TableType0396},
};

#elif HL7_VERSION == 250

const SubFieldSpec CE_SPEC[6] = {
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeID, 20, OPTIONAL, TableType0396},
    {FieldTypeST, 20, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 20, OP<PERSON>ON<PERSON>, TableType0396},
};

#elif HL7_VERSION == 260

const SubFieldSpec CE_SPEC[6] = {
    {FieldTypeST, 0, OP<PERSON>ONAL, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 0, OPTIONAL, TableType0396},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 0, OPTIONAL, TableType0396},
};

#endif