#include "table_0076.h"

#if HL7_VERSION == 240

const char *Table0076Values[] = {
    "ACK", "ADR", "ADT", "BAR", "CRM", "CSU", "DFT", "DOC", "DSR", "EAC", "EAN",
    "EAR", "EDR", "EQQ", "ERP", "ESR", "ESU", "INR", "INU", "LSR", "LSU", "MCF",
    "MDM", "MFD", "MFK", "MFN", "MFQ", "MFR", "NMD", "NMQ", "NMR", "OMD", "OMG",
    "OML", "OMN", "OMP", "OMS", "ORD", "ORF", "ORG", "ORL", "ORM", "ORN", "ORP",
    "ORR", "ORS", "ORU", "OSQ", "OSR", "OUL", "PEX", "PGL", "PIN", "PMU", "PPG",
    "PPP", "PPR", "PPT", "PPV", "PRR", "PTR", "QBP", "QCK", "QCN", "QRY", "QSB",
    "QSX", "QVR", "RAR", "RAS", "RCI", "RCL", "RDE", "RDR", "RDS", "RDY", "REF",
    "RER", "RGR", "RGV", "ROR", "RPA", "RPI", "RPL", "RPR", "RQA", "RQC", "RQI",
    "RQP", "RQQ", "RRA", "RRD", "RRE", "RRG", "RRI", "RSP", "RTB", "SIU", "SPQ",
    "SQM", "SQR", "SRM", "SRR", "SSR", "SSU", "SUR", "TBR", "TCR", "TCU", "UDM",
    "VQQ", "VXQ", "VXR", "VXU", "VXX"};

const HL7Table Table0076 = {.table_name = "message type",
                            .valid_values = Table0076Values,
                            .value_count = 115};

#elif HL7_VERSION == 250

const char *Table0076Values[] = {
    "ACK", "ADR", "ADT", "BAR", "BPS", "BRP", "BRT", "BTS", "CRM", "CSU", "DFT",
    "DOC", "DSR", "EAC", "EAN", "EAR", "EDR", "EQQ", "ERP", "ESR", "ESU", "INR",
    "INU", "LSR", "LSU", "MCF", "MDM", "MFD", "MFK", "MFN", "MFQ", "MFR", "NMD",
    "NMQ", "NMR", "OMB", "OMD", "OMG", "OMI", "OML", "OMN", "OMP", "OMS", "OMS",
    "ORB", "ORD", "ORF", "ORG", "ORI", "ORL", "ORM", "ORN", "ORP", "ORR", "ORS",
    "ORU", "OSQ", "OSR", "OUL", "PEX", "PGL", "PIN", "PMU", "PPG", "PPP", "PPR",
    "PPT", "PPV", "PRR", "PTR", "QBP", "QCK", "QCN", "QRY", "QSB", "QSX", "QVR",
    "RAR", "RAS", "RCI", "RCL", "RDE", "RDR", "RDS", "RDY", "REF", "RER", "RGR",
    "RGV", "ROR", "RPA", "RPI", "RPL", "RPR", "RQA", "RQC", "RQI", "RQP", "RQQ",
    "RRA", "RRD", "RRE", "RRG", "RRI", "RSP", "RTB", "SIU", "SPQ", "SQM", "SQR",
    "SRM", "SRR", "SSR", "SSU", "SUR", "TBR", "TCR", "TCU", "UDM", "VQQ", "VXQ",
    "VXR", "VXU", "VXX",
};

const HL7Table Table0076 = {.table_name = "message type",
                            .valid_values = Table0076Values,
                            .value_count = 124};

#elif HL7_VERSION == 260

const char *Table0076Values[] = {
    "Value", "ACK", "ADR", "ADT", "BAR", "BPS", "BRP", "BRT", "BTS", "CRM",
    "CSU",   "DFT", "DOC", "DSR", "EAC", "EAN", "EAR", "EHC", "ESR", "ESU",
    "INR",   "INU", "LSR", "LSU", "MDM", "MFD", "MFK", "MFN", "MFQ", "MFR",
    "NMD",   "NMQ", "NMR", "OMB", "OMD", "OMG", "OMI", "OML", "OMN", "OMP",
    "OMS",   "OMS", "OPL", "OPR", "OPU", "ORB", "ORD", "ORF", "ORG", "ORI",
    "ORL",   "ORM", "ORN", "ORP", "ORR", "ORS", "ORU", "OSQ", "OSR", "OUL",
    "PEX",   "PGL", "PIN", "PMU", "PPG", "PPP", "PPR", "PPT", "PPV", "PRR",
    "PTR",   "QBP", "QCK", "QCN", "QRY", "QSB", "QSX", "QVR", "RAR", "RAS",
    "RCI",   "RCL", "RDE", "RDR", "RDS", "RDY", "REF", "RER", "RGR", "RGV",
    "ROR",   "RPA", "RPI", "RPL", "RPR", "RQA", "RQC", "RQI", "RQP", "RRA",
    "RRD",   "RRE", "RRG", "RRI", "RSP", "RTB", "SCN", "SDN", "SDR", "SIU",
    "SLN",   "SLR", "SMD", "SQM", "SQR", "SRM", "SRR", "SSR", "SSU", "STC",
    "STI",   "SUR", "TBR", "TCR", "TCU", "UDM", "VXQ", "VXR", "VXU", "VXX",
};

const HL7Table Table0076 = {.table_name = "message type",
                            .valid_values = Table0076Values,
                            .value_count = 130};

#endif
