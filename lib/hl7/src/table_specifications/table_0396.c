#include "table_0396.h"

#if HL7_VERSION == 240

const char *Table0396Values[] = {
    "99zzz", "L",    "ACR",     "ART",  "AS4",  "AS4E",    "ATC",  "C4",
    "C5",    "CAS",  "CD2",     "CDCA", "CDCM", "CDS",     "CE",   "CLP",
    "CPTM",  "CST",  "CVX",     "DCL",  "DCM",  "DQL",     "E",    "E5",
    "E6",    "E7",   "ENZC",    "FDDC", "FDDX", "FDK",     "HB",   "HCPCS",
    "HHC",   "HI",   "HL7nnnn", "HPC",  "I10",  "I10P",    "I9",   "I9C",
    "IBT",   "IC2",  "ICDO",    "ICS",  "ICSD", "ISOnnnn", "IUPC", "IUPP",
    "JC8",   "LB",   "LN",      "MCD",  "MCR",  "MDDX",    "MEDC", "MEDR",
    "MEDX",  "MGPI", "MVX",     "NDA",  "NDC",  "NIC",     "NPI",  "OHA",
    "OHA",   "POS",  "RC",      "SDM",  "SNM",  "SNM3",    "SNT",  "UC",
    "UMD",   "UML",  "UPC",     "UPIN", "W1",   "W2",      "W4",   "WC",
};

const HL7Table Table0396 = {.table_name = "message strcuture",
                            .valid_values = Table0396Values,
                            .value_count = 80};

#elif HL7_VERSION == 250

const char *Table0396Values[] = {
    "99zz", "L",    "ACR",     "ANS",  "ART",     "AS4",     "AS4E",  "ATC",
    "C4",   "C5",   "CAS",     "CD2",  "CDCA",    "CDCM",    "CDS",   "CE",
    "CLP",  "CPTM", "CST",     "CVX",  "DCM",     "E",       "E5",    "E6",
    "E7",   "ENZC", "FDDC",    "FDDX", "FDK",     "HB",      "HCPCS", "HCPT",
    "HHC",  "HI",   "HL7nnnn", "HOT",  "HPC",     "I10",     "I10P",  "I9",
    "I9C",  "IBT",  "IBTnnnn", "IC2",  "ICD10AM", "ICD10CA", "ICDO",  "ICS",
    "ICSD", "ISO",  "ISOnnnn", "IUPC", "IUPP",    "JC10",    "JC8",   "JJ1017",
    "LB",   "LN",   "MCD",     "MCR",  "MDDX",    "MEDC",    "MEDR",  "MEDX",
    "MGPI", "MVX",  "NDA",     "NDC",  "NIC",     "NPI",     "NUBC",  "OHA",
    "OHA",  "POS",  "RC",      "SDM",  "SNM",     "SNM3",    "SNT",   "UC",
    "UMD",  "UML",  "UPC",     "UPIN", "USPS",    "W1",      "W2",    "W4",
    "WC",
};

const HL7Table Table0396 = {.table_name = "message strcuture",
                            .valid_values = Table0396Values,
                            .value_count = 89};

#elif HL7_VERSION == 260

const char *Table0396Values[] = {
    "99\\\\\\",
    "L",
    "ACR",
    "ALPHAID2006",
    "ALPHAID2007",
    "ALPHAID2008",
    "ANS",
    "ART",
    "AS4",
    "AS4E",
    "ATC",
    "C4",
    "C5",
    "CAS",
    "CCC",
    "CD2",
    "CDCA",
    "CDCM",
    "CDS",
    "CE",
    "CLP",
    "CPTM",
    "CST",
    "CVX",
    "DCM",
    "E",
    "E5",
    "E6",
    "E7",
    "ENZC",
    "FDDC",
    "FDDX",
    "FDK",
    "GDRG2004",
    "GDRG2005",
    "GDRG2006",
    "GDRG2007",
    "GDRG2008",
    "GMDC2004",
    "GMDC2005",
    "GMDC2006",
    "GMDC2007",
    "GMDC2008",
    "HB",
    "HCPCS",
    "HCPT",
    "HHC",
    "HI",
    "HL7\\\\\\\\",
    "HOT",
    "HPC",
    "I10",
    "I10G2004",
    "I10G2005",
    "I10G2006",
    "I10P",
    "I9",
    "I9C",
    "I9CDX",
    "I9CP",
    "IBT",
    "IBT\\\\\\\\",
    "ICD10AM",
    "ICD10CA",
    "ICD10GM2007",
    "ICD10GM2008",
    "ICDO",
    "ICS",
    "ICSD",
    "ISO",
    "ISO\\\\\\\\",
    "ITIS",
    "IUPC",
    "IUPP",
    "JC10",
    "JC8",
    "JJ1017",
    "LB",
    "LN",
    "MCD",
    "MCR",
    "MDC",
    "MDDX",
    "MEDC",
    "MEDR",
    "MEDX",
    "MGPI",
    "MVX",
    "NCPDP\\\\\\\\\\\\",
    "NDA",
    "NDC",
    "NIC",
    "NPI",
    "NUBC",
    "O301",
    "O3012004",
    "O3012005",
    "O3012006",
    "OHA",
    "OPS2007",
    "OPS2008",
    "POS",
    "RC",
    "SCT",
    "SCT2",
    "SDM",
    "SNM",
    "SNM3",
    "SNT",
    "UB04FL14",
    "UB04FL15",
    "UB04FL17",
    "UC",
    "UCUM",
    "UMD",
    "UML",
    "UPC",
    "UPIN",
    "USPS",
    "W1",
    "W2",
    "W4",
    "WC",
    "X12DE\\\\\\\\",
};

const HL7Table Table0396 = {.table_name = "message strcuture",
                            .valid_values = Table0396Values,
                            .value_count = 124};

#endif
