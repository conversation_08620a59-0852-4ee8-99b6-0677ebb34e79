#include "table_0301.h"

#if HL7_VERSION == 240

const char *Table0301Values[] = {"DNS",   "GUID",   "HCD",  "HL7",  "ISO",
                                 "L,M,N", "Random", "UUID", "x400", "x500"};

const HL7Table Table0301 = {.table_name = "Universal ID type",
                            .valid_values = Table0301Values,
                            .value_count = 10};

#elif HL7_VERSION == 250 || HL7_VERSION == 260

const char *Table0301Values[] = {"DNS",  "GUID",  "HCD",    "HL7",
                                 "ISO",  "L,M,N", "Random", "URI",
                                 "UUID", "x400",  "x500"};

const HL7Table Table0301 = {.table_name = "Universal ID type",
                            .valid_values = Table0301Values,
                            .value_count = 11};
#endif
