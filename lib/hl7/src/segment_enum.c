#include "segment_enum.h"

#include "log.h"

#define GENERATE_CONVERSION(ENUM) {SegmentType##ENUM, #ENUM},

static const struct {
  SegmentType val;
  const char *str;
} string2st_conversion[] = {FOREACH_SEGMENT_TYPE(GENERATE_CONVERSION)};

SegmentType hl7_str2segment_type(const char *str) {
  log_debug("Converting segment string '%s' to enum...", str ? str : "NULL");
  for (size_t j = 0;
       j < sizeof(string2st_conversion) / sizeof(string2st_conversion[0]); ++j) {
    if (!strcmp(str, string2st_conversion[j].str)) {
      log_debug("Found match: '%s' -> %d", str, string2st_conversion[j].val);
      return string2st_conversion[j].val;
    }
  }

  log_debug("No match found for '%s'", str);
  last_error_set("unknown segment type : %s", str);
  return SegmentTypeUnknown;
}

#define GENERATE_STRING(STRING) #STRING,

static const char *segment_type_string[] = {
    FOREACH_SEGMENT_TYPE(GENERATE_STRING)};

const char *hl7_segment_type2str(SegmentType type) {
  return segment_type_string[type];
}
