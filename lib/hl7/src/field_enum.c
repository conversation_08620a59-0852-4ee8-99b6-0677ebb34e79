#include "field_enum.h"

#include "log.h"

#define GENERATE_CONVERSION(ENUM) {FieldType##ENUM, #ENUM},

static const struct {
  FieldType val;
  const char *str;
} string2st_conversion[] = {FOREACH_FIELD_TYPE(GENERATE_CONVERSION)};

FieldType hl7_str2field_type(const char *str) {
  for (size_t j = 0;
       j < sizeof(string2st_conversion) / sizeof(string2st_conversion[0]); ++j)
    if (!strcmp(str, string2st_conversion[j].str))
      return string2st_conversion[j].val;

  last_error_set("unknown field type : %s", str);

  return FieldTypeUnknown;
}

#define GENERATE_STRING(STRING) #STRING,

static const char *field_type_string[] = {FOREACH_FIELD_TYPE(GENERATE_STRING)};

const char *hl7_field_type2str(FieldType type) {
  return field_type_string[type];
}