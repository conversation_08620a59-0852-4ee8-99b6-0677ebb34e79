#include "message.h"

#include "field.h"
#include "log.h"
#include <assert.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

static void *segment_clone(const void *segment) {
  Segment *seg = (Segment *)segment;
  return hl7_segment_clone(seg);
}

static void segment_destruct(void *segment) { hl7_segment_destruct(segment); }

Message *hl7_message_create() {
  Message *message;
  LOG_CHECK_MALLOC(message, malloc(sizeof(Message)));

  message->segments = dl_create(segment_clone, segment_destruct);
  if (!message->segments) {
    free(message);
    return NULL;
  }

  message->separators.component_separator = COMPONENT_SEPARATOR_DEFAULT;
  message->separators.escape_character = ESCAPE_SEPARATOR_DEFAULT;
  message->separators.field_separator = FIELD_SEPARATOR_DEFAULT;
  message->separators.repetition_separator = REPETITION_SEPARATOR_DEFAULT;
  message->separators.subcomponent_separator = SUBCOMPONENT_SEPARATOR_DEFAULT;

  return message;
}

Message *hl7_message_from_string(const char *str) {
  if (!str) {
    last_error_set("can't create a message from NULL string");
    return NULL;
  }

  // Create a new message
  Message *message = hl7_message_create();
  if (!message)
    return NULL;

  // Tokenize the input string using the segment delimiter '\r' (carriage
  // return)
  const char *segment_delimiter = "\r";
  char *hl7_str = strdup(str); // Make a modifiable copy of the input string
  char *segment_str = strtok(hl7_str, segment_delimiter);

  // Parse each segment
  while (segment_str) {
    // Create a Segment from the segment string
    Segment *segment =
        hl7_segment_from_string(segment_str, &message->separators);
    if (!segment) {
      // Clean up and return NULL if any segment parsing fails
      hl7_message_destruct(message);
      free(hl7_str);
      return NULL;
    }

    // Add the segment to the message
    hl7_message_add_segment(message, segment);

    // Get the next segment
    segment_str = strtok(NULL, segment_delimiter);
  }

  // Clean up
  free(hl7_str);

  return message;
}

char *hl7_message_to_string(const Message *message) {
    log_debug("hl7_message_to_string: Starting message conversion to string");
    
    if (!message || !message->segments) {
        last_error_set("can't create a string from NULL message");
        return NULL;
    }
    
    size_t segments_count = dl_length(message->segments);
    log_debug("hl7_message_to_string: Total segments in message: %zu", segments_count);
    
    if (segments_count == 0) {
        last_error_set("can't create string representation of a message with 0 segments");
        return NULL;
    }
    
    // Array to hold the string representation of each segment
    char **substrs;
    LOG_CHECK_MALLOC(substrs, malloc(segments_count * sizeof(char *)));
    
    
    // Initialize all pointers to NULL for safe cleanup
    for (size_t i = 0; i < segments_count; i++) {
        substrs[i] = NULL;
    }
    
    size_t total_length = 0;
    log_debug("hl7_message_to_string: Converting each segment to string");
    
    // Convert each segment to a string and calculate the total message length
    for (size_t i = 0; i < segments_count; i++) {
        const Segment *segment = dl_get(message->segments, i);
        if (!segment) {
            log_debug("hl7_message_to_string: NULL segment at index %zu", i);
            // Clean up allocated strings
            for (size_t j = 0; j < segments_count; j++) {
                if (substrs[j]) free(substrs[j]);
            }
            free(substrs);
            last_error_set("NULL segment found in message");
            return NULL;
        }
        
        char *child_str = hl7_segment_to_string(segment, &message->separators);
        log_debug("hl7_message_to_string: Segment %zu converted to string: %s", 
                  i, child_str ? child_str : "NULL");
        
        if (!child_str) {
            // Clean up if conversion fails
            for (size_t j = 0; j < segments_count; j++) {
                if (substrs[j]) free(substrs[j]);
            }
            free(substrs);
            last_error_set("Failed to convert segment to string");
            return NULL;
        }
        
        size_t child_len = strlen(child_str);
        total_length += child_len;
        if (i < segments_count - 1) {
            total_length += 1; // +1 for the segment delimiter '\r'
        }
        substrs[i] = child_str;
        
        log_debug("hl7_message_to_string: Segment %zu length: %zu, total so far: %zu", 
                  i, child_len, total_length);
    }
    
    total_length += 1; // null terminator
    
    log_debug("hl7_message_to_string: Allocating %zu bytes for final message", total_length);
    
    // Allocate memory for the final message string
    char *message_str;
    LOG_CHECK_MALLOC(message_str, malloc(total_length));
    log_debug("hl7_message_to_string: AFTER ALLOC: total_length=%zu, segments_count=%zu", total_length, segments_count);
    log_debug("hl7_message_to_string: Message string allocated at %p", (void*)message_str);
    
    // Initialize the string
    message_str[0] = '\0';
    
    // Build the message using strcat for simplicity and safety
    log_debug("hl7_message_to_string: Concatenating segments into final message string");
    
    for (size_t i = 0; i < segments_count; i++) {
        char *child_str = substrs[i];
        
        log_debug("hl7_message_to_string: Adding segment %zu: %s", i, child_str);
        
        // Concatenate the segment string
        log_debug("hl7_message_to_string: BEFORE strcat: length=%zu, still available=%zu", strlen(message_str), total_length-strlen(message_str)-1);

        strcat(message_str, child_str);
        
        // Add the segment delimiter (except for the last segment)
        if (i < segments_count - 1) {
            strcat(message_str, "\r");
        }
        
        log_debug("hl7_message_to_string: After adding segment %zu, message length: %zu", 
                  i, strlen(message_str));
    }
    
    // Clean up segment strings
    for (size_t i = 0; i < segments_count; i++) {
        if (substrs[i]) {
            free(substrs[i]);
        }
    }
    free(substrs);
    
    log_debug("hl7_message_to_string: Conversion completed successfully");
      
    // Debug output - create a safe copy for display
    size_t msg_len = strlen(message_str);
    log_debug("hl7_message_to_string: Final message length: %zu", msg_len);
    // Debug_display_str will have \r replaced by \n for readability otherwise the logger should truncate the data becauee of \r
    char *debug_display_str = malloc(msg_len + 1);
    if (debug_display_str) {
        strcpy(debug_display_str, message_str);
        log_debug("hl7_message_to_string: Creating debug copy of message string. copy message lenght: %zu", strlen(debug_display_str));
        for (size_t i = 0; i < msg_len; i++) {
            if (debug_display_str[i] == '\r') {
                debug_display_str[i] = '\n';
            }
        }
        log_debug("hl7_message_to_string: Final debug message: \n %s", debug_display_str);
        free(debug_display_str);
    }
    
    return message_str;
}

int hl7_message_add_segment(Message *message, Segment *segment) {
  return dl_append_by_value(message->segments, segment);
}

const Segment *hl7_message_get_segment_by_index(const Message *message,
                                                size_t index) {
  return dl_get(message->segments, index);
}

const Segment *hl7_message_get_segment_by_type(const Message *message,
                                               const SegmentType type) {
  for (size_t i = 0; i < dl_length(message->segments); i++) {
    const Segment *segment = dl_get(message->segments, i);
    if (hl7_segment_get_type(segment) == type)
      return segment;
  }

  last_error_set("no segment of type %s in message",
                 hl7_segment_type2str(type));
  return NULL;
}

size_t hl7_message_get_segments_count(const Message *message) {
  return dl_length(message->segments);
}

void hl7_message_destruct(Message *message) {
  if (!message)
    return;

  if (message->segments) {
    dl_destruct(message->segments);
    free(message->segments);
  }

  free(message);
}