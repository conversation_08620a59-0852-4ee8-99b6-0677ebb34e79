#include "pubsub.h"

#include "log.h"
#include <string.h>

// PubSub management functions
void pubsub_init(PubSub *pubsub) {
  pubsub->count = 0;
  memset(pubsub->subscribers, 0, sizeof(pubsub->subscribers));
}

int add_subscriber(PubSub *pubsub, MessageSubscriber subscriber,
                   void *context) {
  if (pubsub->count >= MAX_SUBSCRIBERS) {
    last_error_set("maximum number of subscribers reached (%d)",
                   MAX_SUBSCRIBERS);
    return 1;
  } // Too many subscribers
  pubsub->subscribers[pubsub->count].subscriber = subscriber;
  pubsub->subscribers[pubsub->count].context = context;
  pubsub->count++;

  return 0;
}

void publish_message(PubSub *pubsub, const void *data) {
  for (int i = 0; i < pubsub->count; i++)
    pubsub->subscribers[i].subscriber(pubsub->subscribers[i].context, data);
}