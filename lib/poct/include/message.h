/**
 * @file message.h
 * @brief POCT message object.
 */

#pragma once

#include "common.h"
#include <libxml2/libxml/parser.h>

/**
 * @brief POCT message structure
 */
typedef struct POCTMessageStruct {
  /**
   * @brief topic the message is part of
   */
  POCTTopicType topic;
  POCTMessageType type;
  xmlDocPtr xml_doc;
  int control_id;
  char *timestamp;
  char *version_id;
} POCTMessage;

POCTMessage *poct_message_create();

POCTMessage *poct_message_from_xml_doc(xmlDocPtr xml_doc,
                                       const PoctSpecification *spec);

POCTMessage *poct_message_from_string(const char *str,
                                      const PoctSpecification *spec);

char *poct_message_to_string(const POCTMessage *message);

void poct_message_destruct(POCTMessage *message);