file(GLOB_RECURSE SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/src/*.c")

find_package(LibXml2)

add_library(poct STATIC ${SOURCES})

target_include_directories(
  poct
  PUBLIC include
  PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../application_layer/poct)

if(LibXml2_FOUND)
  target_include_directories(poct PRIVATE ${LIBXML2_INCLUDE_DIRS})
  target_link_libraries(poct dynamic_list log ${LIBXML2_LIBRARIES})
else()
  target_link_libraries(poct dynamic_list log)
endif()
