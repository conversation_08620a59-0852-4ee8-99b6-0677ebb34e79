#include "message.h"

#include "log.h"
#include <libxml2/libxml/xmlsave.h>
#include <stdlib.h>

POCTMessage *poct_message_create() {
  // message allocation
  POCTMessage *message;
  LOG_CHECK_MALLOC(message, malloc(sizeof(POCTMessage)));

  message->control_id = -1;
  message->timestamp = NULL;
  message->topic = -1;
  message->type = -1;
  message->version_id = NULL;
  message->xml_doc = NULL;

  return message;
}

#define REQUIRE_FIELD_NOT(field, value, name, goto_flag)                       \
  if (field == value) {                                                        \
    last_error_set("field " name " missing in message header");                \
    status = 1;                                                                \
    goto goto_flag;                                                            \
  }

static int read_header(POCTMessage *message) {
  int status = 0;
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);

  for (xmlNodePtr cursor = root->children; cursor; cursor = cursor->next) {
    if (cursor->type == XML_ELEMENT_NODE &&
        xmlStrcmp(cursor->name, BAD_CAST "HDR") == 0) {
      // Found the HDR node
      xmlNode *hdr_child = NULL;
      for (hdr_child = cursor->children; hdr_child;
           hdr_child = hdr_child->next) {
        if (hdr_child->type == XML_ELEMENT_NODE) {
          xmlChar *value = xmlGetProp(hdr_child, BAD_CAST "V");
          if (xmlStrcmp(hdr_child->name, BAD_CAST "HDR.control_id") == 0)
            message->control_id = atoi((char *)value);
          else if (xmlStrcmp(hdr_child->name, BAD_CAST "HDR.version_id") == 0)
            message->version_id = strdup((char *)value);
          else if (xmlStrcmp(hdr_child->name, BAD_CAST "HDR.creation_dttm") ==
                   0)
            message->timestamp = strdup((char *)value);
          free(value);
        }
      }
    }
  }

  REQUIRE_FIELD_NOT(message->timestamp, NULL, "timestamp", read_header_clean);
  REQUIRE_FIELD_NOT(message->control_id, -1, "control id", read_header_clean);
  REQUIRE_FIELD_NOT(message->version_id, NULL, "version id", read_header_clean);

read_header_clean:
  if (status) {
    if (message->timestamp) {
      free(message->timestamp);
      message->timestamp = NULL;
    }

    if (message->version_id) {
      free(message->version_id);
      message->version_id = NULL;
    }

    message->control_id = -1;
  }

  return status;
}

static int read_message_type(POCTMessage *message,
                             const PoctSpecification *spec) {
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  message->type = -1;

  for (size_t i = 0; i < spec->messages_length; i++) {
    if (xmlStrcmp(root->name, (const xmlChar *)spec->messages_strings[i]) ==
        0) {
      message->type = spec->messages[i];
      break;
    }
  }

  if (message->type == -1) {
    last_error_set("unknown message type : %s ", root->name);
    return 1;
  }

  return 0;
}

POCTMessage *poct_message_from_xml_doc(xmlDocPtr xml_doc,
                                       const PoctSpecification *spec) {
  if (!xml_doc || !spec) {
    last_error_set(
        "can't create xml message from NULL xml document or specification");
    return NULL;
  }

  int status = 0;
  POCTMessage *message = NULL;

  // message allocation
  message = poct_message_create();
  if (!message) {
    status = 1;
    goto poct_message_from_xml_doc_clean;
  }

  // xml
  message->xml_doc = xml_doc;

  // message type
  if (read_message_type(message, spec)) {
    status = 1;
    goto poct_message_from_xml_doc_clean;
  }

  // header
  if (read_header(message)) {
    status = 1;
    goto poct_message_from_xml_doc_clean;
  }

  if (strcmp(message->version_id, "POCT1") != 0) {
    last_error_set("wrong POCT version : expected POCT1, got %s",
                   message->version_id);
    status = 1;
    goto poct_message_from_xml_doc_clean;
  }

poct_message_from_xml_doc_clean:
  if (status) {
    if (message) {
      poct_message_destruct(message);
      message = NULL;
    } else {
      xmlFreeDoc(xml_doc);
    }
  }

  return message;
}

POCTMessage *poct_message_from_string(const char *str,
                                      const PoctSpecification *spec) {
  xmlDocPtr xml_doc = xmlParseMemory(str, strlen(str));
  if (!xml_doc) {
    last_error_set("document couldn't be parsed as xml");
    return NULL;
  }

  return poct_message_from_xml_doc(xml_doc, spec);
}

char *poct_message_to_string(const POCTMessage *message) {
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);

  xmlBufferPtr buffer = xmlBufferCreate();
  if (!buffer) {
    last_error_set("libxml2 failed to create a buffer");
    return NULL;
  }

  xmlNodeDump(buffer, message->xml_doc, root, 0, 1);
  char *result = strdup((const char *)buffer->content);
  xmlBufferFree(buffer);

  return result;
}

void poct_message_destruct(POCTMessage *message) {
  if (!message)
    return;

  if (message->timestamp)
    free(message->timestamp);

  if (message->version_id)
    free(message->version_id);

  if (message->xml_doc)
    xmlFreeDoc(message->xml_doc);

  free(message);
}
