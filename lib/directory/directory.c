#include "directory.h"

#include "log.h"
#include <dirent.h>
#include <errno.h>
#include <fcntl.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#define PATH_MAX_LEN 2048

// Helper function to check if a directory exists
int directory_exists(const char *path) {
  struct stat statbuf;
  if (stat(path, &statbuf) != 0)
    return 0;

  int is_dir = S_ISDIR(statbuf.st_mode);
  if (!is_dir)
    last_error_set("%s is not a directory", path);

  return is_dir;
}

int can_create_file_in_dir(const char *dir_path) {
  char test_path[PATH_MAX_LEN];

  if (snprintf(test_path, PATH_MAX_LEN, "%s%s", dir_path, "test") >
      PATH_MAX_LEN) {
    last_error_set("can't create file in directory %s : path is too long",
                   dir_path);
    return false;
  }

  FILE *test_fd = fopen(test_path, "w");
  if (!test_fd) {
    last_error_set("can't create file in directory %s", dir_path);
    return false;
  }

  fclose(test_fd);

  if (unlink(test_path) == -1) {
    last_error_set("can't remove test file in directory %s", dir_path);
    return false;
  }

  return true;
}

int directory_exists_and_is_accessible(const char *path) {
  if (!directory_exists(path))
    return false;

  return can_create_file_in_dir(path);
}

int create_directory(const char *path) {
  if (directory_exists(path))
    return 0;

  // Attempt to create the directory with full read/write/execute permissions
  if (mkdir(path, 0755) == 0)
    return 0;

  last_error_set("can't create directory %s", path);

  return 1;
}

int directory_is_writable(const char *path) {
  if (!directory_exists(path)) {
    if (create_directory(path) == -1)
      return -1;
  }

  return can_create_file_in_dir(path);
}