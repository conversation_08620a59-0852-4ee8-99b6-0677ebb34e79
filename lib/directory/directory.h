/**
 * @file directory.h
 * @brief Miscellaneous functions used to work with directories.
 */

#pragma once

/**
 * @brief Test whether a directory exists or not.
 *
 * @param path Path to the directory.
 *
 * @return
 * - Success : 1 if the directory exists, 0 otherwise
 * - Error : -1
 */
int directory_exists(const char *path);

/**
 * @brief Check that a given directory exists and that we can create a file in
 * this directory.
 *
 * @warning Please note that in order to test whether it is possible to create a
 * file in the directory, this function tries to create a file
 * called "test-file". However, this file, if created, is then removed.
 *
 * @param path Path to the directory.
 *
 * @return
 * - Success : 1 if the directory exists and it is possible to create a file in
 * it. 0 otherwise
 * - Error : -1
 */
int directory_exists_and_is_accessible(const char *path);

/**
 * @brief Check that a given directory can be written to. If the directory
 * doesn't exist, it tries to create it.
 *
 * @warning Please note that in order to test whether it is possible to create a
 * file in the directory, this function tries to create a file
 * called "test-file". However, this file, if created, is then removed.
 *
 * @param path Path to the directory.
 *
 * @return
 * - Success : 1 if the directory can be written to. 0 otherwise
 * - Error : -1
 */
int directory_is_writable(const char *path);

/**
 * @brief Tries to create a directory.
 *
 * If the directory already exists, returns 1.
 *
 * @param path Path to the directory.
 *
 * @return
 * - Success : 0 if the directory was created. 1 otherwise
 * - Error : -1
 */
int create_directory(const char *path);