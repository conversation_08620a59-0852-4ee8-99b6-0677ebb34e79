/**
 * @file buffer_handler.h
 * @brief Handles a binary buffer to communicate with transport level.
 *
 * Protocol messages are considered encapsulated as so :
 * <VT>...<FS>
 */

#pragma once

#include "log.h"
#include <stdbool.h>
#include <stddef.h>
#include <stdlib.h>

typedef struct BinaryBufferStruct {
  unsigned char *buffer;
  size_t length;
  size_t capacity;
} BinaryBuffer;

typedef struct BinaryBufferDocumentStruct {
  size_t start;
  size_t end;
} BinaryBufferDocument;

/// @brief Creates a binary buffer from a provided buffer.
///
/// The provided buffer is considered caller-owned, and will thus not be freed
/// on binary buffer destruction.
///
/// @param buffer pre-allocated buffer
/// @param capacity capacity of `buffer`
///
/// @return
/// - Success : the created binary buffer
/// - Error : NULL
BinaryBuffer *bb_create(unsigned char *buffer, size_t capacity);

/// @brief Creates a binary buffer from a provided buffer and a string.
///
/// The buffer will construct a binary message from string, for sending over
/// protocol level. The provided buffer is considered caller-owned, and will
/// thus not be freed on binary buffer destruction.
///
/// @note `capacity` should be at least the length of `str` + 4
///
/// @param str textual representation of message
/// @param buffer pre-allocated buffer
/// @param capacity capacity of `buffer`
///
/// @return
/// - Success : the created binary buffer
/// - Error : NULL
BinaryBuffer *bb_from_string(const char *str, unsigned char *buffer,
                             size_t capacity);

/// @brief Appends bytes to the binary buffer.
///
/// @param bb
/// @param data raw bytes
/// @param length length of `data
///
/// @return
/// - Success : 0
/// - Error : 1. The buffer is reset.
int bb_append(BinaryBuffer *bb, const unsigned char *data, size_t length);

/// @brief Tries to transform binary buffer to documents.
///
/// @param bb
///
/// @return
/// - Success : list of documents, NULL terminated (list can be empty).
/// - Error : NULL. The buffer is reset.
BinaryBufferDocument **bb_to_documents_try(BinaryBuffer *bb);

/// @brief Transforms a document to its string representation.
///
/// @param bb
/// @param doc document retrieved through a call to `bb_to_documents_try`
///
/// @return readable string
const char *bb_document_to_string(BinaryBuffer *bb,
                                  const BinaryBufferDocument *doc);

/// @brief Transforms a document to a protocol-sendable bytes document.
///
/// @param bb
/// @param doc document retrieved through a call to `bb_to_documents_try`
/// @param length pointer to store the returned bytes length
///
/// @return bytes starting with <VT> and ending with <FS>
const unsigned char *bb_document_to_bytes(BinaryBuffer *bb,
                                          const BinaryBufferDocument *doc,
                                          size_t *length);

/// @brief Empties the binary buffer.
///
/// @param bb
void bb_clean(BinaryBuffer *bb);

/// @brief Free ressources associated with the binary buffer.
///
/// @param bb
void bb_destruct(BinaryBuffer *bb);
