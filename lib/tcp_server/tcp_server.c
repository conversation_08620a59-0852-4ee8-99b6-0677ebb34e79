#include "tcp_server.h"

#include "log.h"
#include <arpa/inet.h>
#include <errno.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <unistd.h>

int tcp_server_init(TcpServer *tcp_server) {
  struct sockaddr_in server; // server address information

  const int socket_accept = socket(AF_INET, SOCK_STREAM, 0);
  if (socket_accept == -1) {
    last_error_set("can't create the listening socket (errno: %d - %s)", errno,
                   strerror(errno));
    return 1;
  }

  // SO_REUSEADDR and SO_REUSEPORT allow for reuse of the socket,
  // even after a dirty ending (e.g. crash)

  if (tcp_server->reuse_address) {
    if (setsockopt(socket_accept, SOL_SOCKET, SO_REUSEADDR, &(int){1},
                   sizeof(int)) < 0) {
      last_error_set("can't activate the SO_REUSEADDR option (errno: %d - %s)",
                     errno, strerror(errno));
      return 1;
    }
  }

  if (tcp_server->reuse_port) {
    if (setsockopt(socket_accept, SOL_SOCKET, SO_REUSEPORT, &(int){1},
                   sizeof(int)) < 0) {
      last_error_set("can't activate the SO_REUSEPORT option (errno %d - %s)",
                     errno, strerror(errno));
      return 1;
    }
  }

  // bind the socket to the server address
  server.sin_family = AF_INET;
  server.sin_port = htons(tcp_server->port);
  server.sin_addr.s_addr =
      NULL == tcp_server->ip ? INADDR_ANY : inet_addr(tcp_server->ip);

  if (bind(socket_accept, (struct sockaddr *)&server, sizeof(server)) < 0) {
    last_error_set("can't link listening socket to port %d (errno: %d - %s)",
                   tcp_server->port, errno, strerror(errno));
    return 1;
  }

  if (listen(socket_accept, tcp_server->max_connexions) != 0) {
    last_error_set("can't use listening socket (errno: %d - %s)", errno,
                   strerror(errno));
    return 1;
  }

  tcp_server->socket = socket_accept;
  return 0;
}

static int set_socket_nonblocking(int socket_fd) {
  int flags = fcntl(socket_fd, F_GETFL, 0);
  if (flags == -1) {
    last_error_set("fcntl(F_GETFL) failed");
    return 1;
  }

  if (fcntl(socket_fd, F_SETFL, flags | O_NONBLOCK) == -1) {
    last_error_set("fcntl(F_SETFL) failed to set non-blocking mode");
    return 1;
  }

  return 0;
}

int tcp_server_listen_for_connexion_requests(TcpServer *tcp_server,
                                             struct sockaddr_in *client,
                                             int *namelen, const bool *stop) {
  *namelen = sizeof(struct sockaddr_in);

  // Make sure the socket is non-blocking
  if (set_socket_nonblocking(tcp_server->socket))
    return -1;

  int client_socket;

  while (1) {
    // use the `stop` flag to break out of the loop
    if (*stop)
      return -1;

    client_socket = accept(tcp_server->socket, (struct sockaddr *)client,
                           (socklen_t *)namelen);

    if (client_socket == -1) {
      if (errno == EAGAIN || errno == EWOULDBLOCK) {
        // No connection ready yet, wait and retry
        usleep(100 * 1000); // Sleep for 100 milliseconds and try again
        continue;
      } else {
        // A real error occurred
        last_error_set("can't accept client connection request (errno:%d - %s)",
                       errno, strerror(errno));
        return -1;
      }
    }

    // If we get here, a client has connected
    break;
  }

  // reading timeout setting on client socket
  if (tcp_server->read_timeout != 0) {
    const struct timeval tv = {.tv_sec = tcp_server->read_timeout,
                               .tv_usec = 0};
    if (setsockopt(client_socket, SOL_SOCKET, SO_RCVTIMEO, (const char *)&tv,
                   sizeof tv) == -1) {
      last_error_set(
          "can't set reading timeout on client socket (errno:%d - %s)", errno,
          strerror(errno));
      close(client_socket);
      return -1;
    }
  }

  return client_socket;
}

unsigned char *tcp_server_read(int client_socket, size_t buffer_capacity,
                               int *connexion_closed, ssize_t *count) {
  *connexion_closed = 0;

  // allocate the buffer
  unsigned char *buffer;
  LOG_CHECK_MALLOC(buffer,
                   malloc(sizeof(unsigned char) * (buffer_capacity + 1)));
  memset(buffer, 0, (buffer_capacity + 1));

  // read bytes from the socket
  *count = recv(client_socket, buffer, buffer_capacity, 0);
  if (*count == -1) {
    last_error_set("can't read bytes from the client (errno:%d - %s) - "
                   "errno=11 probably means the client closed the connection",
                   errno, strerror(errno));
    free(buffer);
    return NULL;
  }

  if (*count == 0) {
    // the connexion has been closed by the client
    *connexion_closed = 1;
  }

  return buffer;
}

int tcp_server_send(int client_socket, const unsigned char *buffer,
                    size_t length) {
  ssize_t sent_bytes = send(client_socket, buffer, length, 0);

  if (sent_bytes < 0) {
    if (errno == EPIPE || errno == ECONNRESET) {
      last_error_set("socket closed by peer");
    } else {
      last_error_set("can't send bytes to the client (errno:%d - %s)", errno,
                     strerror(errno));
    }
    return 1;
  }

  return 0;
}

void tcp_server_set_reuse_address(TcpServer *tcp_server, int reuse_address) {
  tcp_server->reuse_address = reuse_address;
}

void tcp_server_set_reuse_port(TcpServer *tcp_server, int reuse_port) {
  tcp_server->reuse_port = reuse_port;
}

void tcp_server_set_read_timeout(TcpServer *tcp_server, time_t timeout) {
  tcp_server->read_timeout = timeout;
}

TcpServer *tcp_server_create(const char *ip, const unsigned short port) {
  TcpServer *tcp_server;
  LOG_CHECK_MALLOC(tcp_server, malloc(sizeof(TcpServer)));

  // Initialise the object's attributes.
  tcp_server->ip = NULL;
  // clang-format off
  if (ip) {
    LOG_CHECK_MALLOC(tcp_server->ip, strdup((char *)ip));
  }
  // clang-format on

  tcp_server->port = port;
  tcp_server->reuse_address = 1;
  tcp_server->reuse_port = 1;
  tcp_server->socket = -1;
  tcp_server->max_connexions = 1;
  tcp_server->read_timeout = 0;

  return tcp_server;
}

void tcp_server_terminate(TcpServer *tcp_server) {
  if (tcp_server->socket != -1)
    close(tcp_server->socket);

  tcp_server->socket = -1;
}

void tcp_server_destruct(TcpServer *tcp_server) {
  if (!tcp_server)
    return;

  tcp_server_terminate(tcp_server);

  if (tcp_server->ip)
    free(tcp_server->ip);

  free(tcp_server);
}