cmake_minimum_required(VERSION 3.20)

project(synlab_automatons_drivers VERSION 1.0)
#set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address -fno-omit-frame-pointer")

set(CMAKE_C_STANDARD 17)
set(CMAKE_C_STANDARD_REQUIRED True)
set(CMAKE_BUILD_TYPE Debug)
set(CMAKE_C_FLAGS_DEBUG "-g")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
cmake_minimum_required(VERSION 3.20)

# WARNING the following instruction breaks gdb paths -> your IDE won´t be able
# to redirect you to error lines
#
# for VSCODE, add this to `launch.json` configurations : "setupCommands": [ {
# "description": "Set source directories", "text": "dir ${workspaceFolder}", //
# Adjust to the correct root directory of your source files "ignoreFailures":
# true } ]

# Apply -ffile-prefix-map to convert absolute paths to relative paths for
# __FILE__
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -ffile-prefix-map=${CMAKE_SOURCE_DIR}=.")

# warnings
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -pedantic")

# includes in main folder
include_directories(
  include
  include/transport_layer/tcp
  include/transport_layer/rs232
  include/application_layer/hl7
  include/application_layer/poct
  include/application_layer/poct/drivers
  include/storing_layer/file_storing
  include/interfaces)

# ##############################################################################
# EXTERNAL LIBRAIRIES
# ##############################################################################

add_subdirectory(external/argtable3)
add_subdirectory(external/log)

# ##############################################################################
# INTERNAL LIBRAIRIES
# ##############################################################################

add_subdirectory(lib/binary_buffer)
add_subdirectory(lib/daemon)
add_subdirectory(lib/directory)
add_subdirectory(lib/dynamic_list)
add_subdirectory(lib/hl7)
add_subdirectory(lib/input_output)
add_subdirectory(lib/poct)
add_subdirectory(lib/pubsub)
add_subdirectory(lib/tcp_client)
add_subdirectory(lib/tcp_server)

add_subdirectory(src)

# ##############################################################################
# CPPCHECK
# ##############################################################################

# Check if cppcheck is available
find_program(CPPCHECK_EXECUTABLE cppcheck)

if(CPPCHECK_EXECUTABLE)
  message(STATUS "Found cppcheck: ${CPPCHECK_EXECUTABLE}")

  # Set cppcheck options
  set(CPPCHECK_OPTIONS
      "--enable=all" # Enable all checks
      "--suppress=missingIncludeSystem" # cppcheck doesn't find standard
                                        # librairies
      "--check-level=exhaustive" # recurse branches
      "--inconclusive" # Include inconclusive results
      "--force" # Force checking all files
      "--std=c17" # Specify C standard, adjust as needed
      "--language=c" # Explicitly specify C language
      -I${CMAKE_SOURCE_DIR}/include
      -I${CMAKE_SOURCE_DIR}/include/transport_layer/tcp
      -I${CMAKE_SOURCE_DIR}/include/transport_layer/rs232
      -I${CMAKE_SOURCE_DIR}/include/application_layer/hl7
      -I${CMAKE_SOURCE_DIR}/include/application_layer/poct
      -I${CMAKE_SOURCE_DIR}/include/application_layer/poct/drivers
      -I${CMAKE_SOURCE_DIR}/include/storing_layer/file_storing
      -I${CMAKE_SOURCE_DIR}/include/interfaces
      -I${CMAKE_SOURCE_DIR}/lib/binary_buffer
      -I${CMAKE_SOURCE_DIR}/lib/daemon
      -I${CMAKE_SOURCE_DIR}/lib/directory
      -I${CMAKE_SOURCE_DIR}/lib/dynamic_list
      -I${CMAKE_SOURCE_DIR}/lib/hl7
      -I${CMAKE_SOURCE_DIR}/lib/input_output
      -I${CMAKE_SOURCE_DIR}/lib/poct
      -I${CMAKE_SOURCE_DIR}/lib/pubsub
      -I${CMAKE_SOURCE_DIR}/lib/tcp_client
      -I${CMAKE_SOURCE_DIR}/lib/tcp_server
      -I${CMAKE_SOURCE_DIR}/external/argtable3
      -I${CMAKE_SOURCE_DIR}/external/log)

  # Add cppcheck as a custom target, running on src/, lib/, and external/
  # directories
  add_custom_target(
    cppcheck
    COMMAND ${CPPCHECK_EXECUTABLE} ${CPPCHECK_OPTIONS} ${CMAKE_SOURCE_DIR}/src
            ${CMAKE_SOURCE_DIR}/lib
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    COMMENT "Running cppcheck"
    VERBATIM)

  # # Ensure cppcheck runs before building the main target add_dependencies(main
  # cppcheck)
else()
  message(WARNING "cppcheck not found, skipping static analysis")
endif()
