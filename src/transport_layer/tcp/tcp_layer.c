#include "tcp_layer.h"

#include "application_to_transport.h"
#include "log.h"
#include "tcp_server.h"
#include "transport_to_application.h"
#include <netinet/in.h>
#include <stdbool.h>
#include <stdlib.h>

#define TCP_MAX_SIZE 1024 * 1000

static TransportConfiguration *config;
static PubSub *transport2application;
static PubSub *application2transport;
static TcpServer *server;
static bool stop_flag;
static int client_socket = -1;

int transport_layer_init(TransportConfiguration *init_config,
                         PubSub *init_transport2application,
                         PubSub *init_application2transport) {
  config = init_config;
  transport2application = init_transport2application;
  application2transport = init_application2transport;
  stop_flag = false;

  if (add_subscriber(application2transport, transport_layer_send, NULL))
    return 1;

  return 0;
}

int transport_layer_read_publish_loop() {
  int status = 0;

  // create the TCP server
  server =
      tcp_server_create(config->bind_ip, (unsigned short)config->bind_port);

  if (!server) {
    last_error_display(LOG_FATAL, "can't create TCP server");
    status = 1;
    goto ce_run;
  }

  tcp_server_set_read_timeout(server, config->recv_timeout);

  // server connection
  if (tcp_server_init(server)) {
    last_error_display(LOG_FATAL, "can't initialize TCP server");
    status = 1;
    goto ce_run;
  }

  while (true) {
    struct sockaddr_in client;
    int namelen;

    log_space(LOG_INFO);
    log_info("waiting for incoming connection requests...");
    log_space(LOG_INFO);

    client_socket = tcp_server_listen_for_connexion_requests(
        server, &client, &namelen, &stop_flag);
    if (client_socket == -1) {
      last_error_display(LOG_FATAL,
                         "error during client connection request reception");
      status = 1;
      goto ce_run;
    }

    log_debug("connection request accepted (socket ID : %d)", client_socket);

    // receiving client messages
    while (1) {
      int connection_closed = 0;
      ssize_t bytes_received_count = 0;

      // waiting for a message (blocking)
      log_next_is_start();
      log_debug("waiting for client message...");

      unsigned char *message =
          tcp_server_read(client_socket, TCP_MAX_SIZE, &connection_closed,
                          &bytes_received_count);

      if (!message) {
        last_error_display(LOG_ERROR, "error during message reception");
        break;
      }

      // connection closed by client
      if (connection_closed == 1) {
        log_info("client closed the connection");
        free(message);
        break;
      }

      const TransportToApplicationData data = {
          .bytes = message,
          .length = bytes_received_count,
      };
      publish_message(transport2application, &data);

      free(message);
    }
  }

ce_run:
  if (server)
    tcp_server_destruct(server);

  if (client_socket != -1)
    close(client_socket);

  return status;
}

// TODO: feedback to application layer on transport layer failure
void transport_layer_send(void *void_context, const void *void_data) {
  (void)void_context;
  const ApplicationToTransportData *data =
      (ApplicationToTransportData *)void_data;
  log_start(LOG_DEBUG, "sending message on IP %s PORT %d...", config->bind_ip,
            config->bind_port);
  if (tcp_server_send(client_socket, data->bytes, data->length)) {
    last_error_display(LOG_ERROR,
                       "error while sending data through TCP socket");
  }
  log_end(LOG_DEBUG, "done.");
}

void transport_layer_end() {
  stop_flag = true;
  tcp_server_destruct(server);
}
