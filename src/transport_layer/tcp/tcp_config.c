#include "tcp_config.h"

#include "log.h"
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024

void transport_configuration_init(TransportConfiguration *config) {
  config->bind_ip = NULL;
  config->bind_port = -1;
  config->recv_timeout = -2;
}

#define FREE_AND_SET_NULL(attribute)                                           \
  if (attribute) {                                                             \
    free(attribute);                                                           \
    attribute = NULL;                                                          \
  }

void transport_configuration_destruct(TransportConfiguration *config) {
  if (!config)
    return;

  FREE_AND_SET_NULL(config->bind_ip);
}

char *transport_configuration_to_string(const TransportConfiguration *config) {
  // Allocate a buffer to store the resulting string
  char *result;
  LOG_CHECK_MALLOC(result, malloc(BUFFER_SIZE));

  // Initialize the string
  snprintf(result, BUFFER_SIZE,
           "Configuration:\n"
           "  bind_ip: %s\n"
           "  bind_port: %ld\n"
           "  recv_timeout: %ld\n",
           !config->bind_ip ? "NULL" : config->bind_ip, config->bind_port,
           config->recv_timeout);

  return result;
}