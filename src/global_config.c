#include "global_config.h"

#include "log.h"
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024

void global_configuration_init(GlobalConfiguration *config) {
  config->log_level = NULL;
  config->daemonize = CONFIG_DAEMONIZE_INIT;
  config->log_file = NULL;
  config->stop_after = CONFIG_STOP_AFTER_INIT;
}

#define FREE_AND_SET_NULL(attribute)                                           \
  if (attribute) {                                                             \
    free(attribute);                                                           \
    attribute = NULL;                                                          \
  }

void global_configuration_destruct(GlobalConfiguration *config) {
  if (!config)
    return;

  FREE_AND_SET_NULL(config->log_level);
  FREE_AND_SET_NULL(config->log_file);
}

char *global_configuration_to_string(const GlobalConfiguration *config) {
  // Allocate a buffer to store the resulting string
  char *result;
  LOG_CHECK_MALLOC(result, malloc(BUFFER_SIZE));

  // Initialize the string
  snprintf(result, BUFFER_SIZE,
           "Global configuration:\n"
           "  log_level: %s\n"
           "  log_file: %s\n"
           "  daemonize: %d\n"
           "  stop_after: %ld\n",
           !config->log_level ? "NULL" : config->log_level,
           !config->log_file ? "NULL" : config->log_file, config->daemonize,
           config->stop_after);

  return result;
}
