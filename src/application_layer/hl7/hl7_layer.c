#include "hl7_layer.h"
#include "application_to_storing.h"
#include "application_to_transport.h"
#include "binary_buffer.h"
#include "hl7.h"
#include "hl7_config.h"
#include "log.h"
#include "transport_to_application.h"
#include <stdlib.h>
#include <string.h>
#include <sys/wait.h>
#include <unistd.h>
#include <time.h>

#define BUFFER_SIZE 1024 * 1000

// unlike input_output model, this module doesn't use
// dynamic allocationn
// this choice was taken considering embedded systems requirements
// static allocation allows compile-time memory allocation,
// helping to prevent running short of memory at runtime
// schema is : Receving message → Storage (binary + text) → Parsing → Validation → Sending ACK (MSH+MSA)

static unsigned char buffer[BUFFER_SIZE];
BinaryBuffer *bb;

static ApplicationConfiguration *config;
static PubSub *transport2application;
static PubSub *application2transport;
static PubSub *application2storing;

#if HL7_VERSION == 260 || HL7_VERSION == 250 || HL7_VERSION == 240

Message *generate_ack(const Message *message, const int success,
                      const char *sending_application,
                      const char *sending_facility) {
  log_debug("generate_ack: Starting ACK generation");
  log_debug("generate_ack: Parameters - success=%d, sending_application=%s, sending_facility=%s", 
            success, sending_application ? sending_application : "NULL", 
            sending_facility ? sending_facility : "NULL");

  const Segment *sender_msh_segment =
      hl7_message_get_segment_by_type(message, SegmentTypeMSH);
  const Separators *separators = &message->separators;

  log_debug("generate_ack: MSH segment found: %s", sender_msh_segment ? "YES" : "NO");

  // is there an msh in sending application message ?
  if (!sender_msh_segment) {
    last_error_set("can't generate ACK : no MSH segment in sender message");
    log_debug("generate_ack: Returning NULL - no MSH segment");
    return NULL;
  }

  log_debug("generate_ack: Extracting message type from MSH");
  const Field *message_type =
      hl7_segment_get_field(sender_msh_segment, MSHMessageType);
  const Field *event_type = hl7_field_get_child(message_type, 1);

  if (!event_type) {
    last_error_display(LOG_ERROR, "can't get event type from sender message");
    log_debug("generate_ack: Returning NULL - no event type");
    return NULL;
  }

  log_debug("generate_ack: Converting event type to string");
  char *event_type_str = hl7_field_to_string(event_type, separators);
  char *message_type_str;

  // Check if event_type_str is empty
  if (!event_type_str) {
    last_error_display(LOG_ERROR, "event_type_str is null");
    log_debug("generate_ack: Returning NULL - event_type_str is null");
    return NULL;
  }

  log_debug("generate_ack: Event type extracted: %s", event_type_str);

  // Not necessary to check if event_type_str > SIZE_MAX -9 - not possible
  LOG_CHECK_MALLOC(message_type_str, malloc(strlen(event_type_str) + 9));
  message_type_str[0] = '\0';
  strcat(message_type_str, "ACK^");
  strcat(message_type_str, event_type_str);
  strcat(message_type_str, "^ACK");
  free(event_type_str);

  log_debug("generate_ack: Message type string created: %s", message_type_str);

  // msh segment
  log_debug("generate_ack: Creating ACK MSH segment");
  Segment *ack_msh = hl7_segment_create();

  hl7_segment_set_type(ack_msh, SegmentTypeMSH);
  log_debug("generate_ack: Setting MSH fields. ack_msh value : %s", ack_msh ? "YES" : "NO");

  if (hl7_segment_copy_field(ack_msh, MSHFieldSeparator, sender_msh_segment,
                             MSHFieldSeparator) ||
      hl7_segment_copy_field(ack_msh, MSHEncodingCharacters, sender_msh_segment,
                             MSHEncodingCharacters) ||
      hl7_segment_set_field_string(ack_msh, sending_application, separators,
                                   MSHSendingApplication) ||
      hl7_segment_set_field_string(ack_msh, sending_facility, separators,
                                   MSHSendingFacility) ||
      hl7_segment_copy_field(ack_msh, MSHReceivingApplication,
                             sender_msh_segment, MSHSendingApplication) ||
      hl7_segment_copy_field(ack_msh, MSHReceivingFacility, sender_msh_segment,
                             MSHSendingFacility) ||
      hl7_segment_set_field_string(ack_msh, hl7_generate_date_time(),
                                   separators, MSHDateTimeOfMessage) ||
      hl7_segment_set_field_string(ack_msh, message_type_str, separators,
                                   MSHMessageType) ||
      hl7_segment_set_field_string(ack_msh, hl7_generate_control_id(),
                                   separators, MSHMessageControlID) ||
      hl7_segment_set_field_string(ack_msh, "P", separators, MSHProcessingID) ||
#if HL7_VERSION == 260
      hl7_segment_set_field_string(ack_msh, "2.6", separators, MSHVersionID)
#elif HL7_VERSION == 250
      hl7_segment_set_field_string(ack_msh, "2.5", separators, MSHVersionID)
#elif HL7_VERSION == 240
      hl7_segment_set_field_string(ack_msh, "2.4", separators, MSHVersionID)
#endif
  ) {
    hl7_segment_destruct(ack_msh);
    return NULL;
  }

  log_debug("generate_ack: MSH fields set. Call free(message_type_str)");
  free(message_type_str);

  // msa segment
  log_debug("generate_ack: Creating ACK MSA segment");
  Segment *ack_msa = hl7_segment_create();
  if (!ack_msa) {
    hl7_segment_destruct(ack_msh);
    return NULL;
  }

  log_debug("generate_ack: Setting MSA type");
  hl7_segment_set_type(ack_msa, SegmentTypeMSA);
  int status;
  log_debug("generate_ack: Setting MSA fields");
  if (success) // At this time it is always success = true
    status = hl7_segment_set_field_string(ack_msa, "CA", separators,
                                          MSAAcknowledgmentCode);
  else
    status = hl7_segment_set_field_string(ack_msa, "CR", separators,
                                          MSAAcknowledgmentCode);
  if (status || hl7_segment_set_field_string(ack_msa, hl7_generate_control_id(),
                                             separators, MSAMessageControlID)) {
    hl7_segment_destruct(ack_msh);
    hl7_segment_destruct(ack_msa);

    return NULL;
  }

  // message
  log_debug("generate_ack: Creating ACK message");
  Message *ack = hl7_message_create();
  if (!ack) {
    hl7_segment_destruct(ack_msh);
    hl7_segment_destruct(ack_msa);
  }

  log_debug("generate_ack: Adding segments to message");
  if (hl7_message_add_segment(ack, ack_msh) ||
      hl7_message_add_segment(ack, ack_msa)) {
    hl7_segment_destruct(ack_msh);
    hl7_segment_destruct(ack_msa);
    hl7_message_destruct(ack);

    return NULL;
  }

  log_debug("generate_ack: Validating ACK message");
  char *error_message = hl7_message_is_valid(ack);
  log_debug("generate_ack: Validation result: %s", error_message ? error_message : "NULL");
  if (!error_message) {
    last_error_display(LOG_ERROR, "couldn't validate acknowledgment");
  } else if (strcmp(error_message, "") != 0) {
    log_warn("acknowledgment doesn't follow HL7 %d standard (this can be due "
             "to original message not following standard):\n%s",
             HL7_VERSION, error_message);
  } else {
    log_info("acknowledgment follows HL7 %d standard", HL7_VERSION);
  }

  if (error_message)
    free(error_message);

  return ack;
}

#endif

int application_layer_init(ApplicationConfiguration *init_config,
                           PubSub *init_transport2application,
                           PubSub *init_application2transport,
                           PubSub *init_application2storing) {
  config = init_config;
  
  // Add validation logs
  log_debug("Config validation - sending_application: %s", 
            config->sending_application ? config->sending_application : "NULL");
  log_debug("Config validation - sending_facility: %s", 
            config->sending_facility ? config->sending_facility : "NULL");
  log_debug("Config validation - delay_before_ack: %ld", config->delay_before_ack);
  transport2application = init_transport2application;
  application2transport = init_application2transport;
  application2storing = init_application2storing;

  bb = bb_create(buffer, BUFFER_SIZE);
  if (!bb) {
    last_error_display(LOG_ERROR, "can't create binary buffer");
    return 1;
  }

  if (add_subscriber(transport2application, application_layer_process_message,
                     NULL)) {

    last_error_display(LOG_ERROR, "can't subscribe");
    return 1;
  }

  return 0;
}

static int send_acknowledgment(const Message *message, const int succes) {
  int status = 0;
  BinaryBuffer *bb_tmp = NULL;
  Message *ack = NULL;
  unsigned char *bytes = NULL;
  char *ack_str = NULL;
  BinaryBufferDocument **docs = NULL;

  if (succes) {
    log_info("constructing acknowledgment for a valid message...");
  } else {
    log_info("constructing acknowledgment for an invalid message...");
  }

  // build acknowledgment message
  log_debug("Calling generate_ack...");
  ack = generate_ack(message, succes, config->sending_application,
                     config->sending_facility);
  if (!ack) {
    log_error("generate_ack failed");
    status = 1;
    goto send_acknowledgment_clean;
  }
  log_debug("generate_ack succeeded");

  // convert to string
  log_debug("Converting ACK to string...");
  ack_str = hl7_message_to_string(ack);
  if (!ack_str) {
    log_error("hl7_message_to_string failed");
    status = 1;
    goto send_acknowledgment_clean;
  }
  log_debug("ACK string conversion succeeded");

  log_debug("sending acknowledgment : \n%s", ack_str);

  // create binary buffer
  size_t str_len = strlen(ack_str);
  size_t bytes_count = str_len + 4;
  LOG_CHECK_MALLOC(bytes, malloc(bytes_count));
  LOG_CHECK_MALLOC(bb_tmp, bb_from_string(ack_str, bytes, bytes_count));

  // send to transport layer
  docs = bb_to_documents_try(bb_tmp);
  if (!docs[0] || docs[1]) {
    log_error("one document should have been created");
    status = 1;
    goto send_acknowledgment_clean;
  }
  size_t length;
  const unsigned char *buffer_bytes =
      bb_document_to_bytes(bb_tmp, docs[0], &length);
  ApplicationToTransportData data = {.bytes = buffer_bytes, .length = length};
  publish_message(application2transport, &data);

send_acknowledgment_clean:
  if (bb_tmp)
    bb_destruct(bb_tmp);
  if (bytes)
    free(bytes);
  if (ack_str)
    free(ack_str);
  if (ack)
    hl7_message_destruct(ack);
  if (docs) {
    for (BinaryBufferDocument **ptr = docs; *ptr; ptr++)
      free(*ptr);
    free(docs);
  }

  return status;
}

// Structure pour stocker les données patient
typedef struct {
  char *query_id;
  char *status;
  char *temps_ms;
  char *civilite;
  char *nom;
  char *prenom;
  char *date_naissance;
  char *sexe;
} PatientData;

static void patient_data_free(PatientData *data) {
  if (!data) return;
  free(data->query_id);
  free(data->status);
  free(data->temps_ms);
  free(data->civilite);
  free(data->nom);
  free(data->prenom);
  free(data->date_naissance);
  free(data->sexe);
  free(data);
}

static void store_binary(const BinaryBufferDocument *doc, const char *ts) {
  size_t length;
  const unsigned char *data = bb_document_to_bytes(bb, doc, &length);
  BinaryFileData data_bin = {.bytes = data, .length = length};
  ApplicationToStoringData storing_data_bin = {.file_data = &data_bin,
                                               .file_type = FT_BINARY,
                                               .folder = "hl7",
                                               .name = ts};
  publish_message(application2storing, &storing_data_bin);
}

static void store_textual(const BinaryBufferDocument *doc, const char *ts) {
  const char *str = bb_document_to_string(bb, doc);
  TextualFileData data_text = {.message = str};

  ApplicationToStoringData storing_data_txt = {.file_data = &data_text,
                                               .file_type = FT_TEXTUAL,
                                               .folder = "hl7",
                                               .name = ts};
  publish_message(application2storing, &storing_data_txt);
}

static PatientData *get_patient_data(const char *query_id) {
  if (!query_id) {
    log_error("get_patient_data: query_id is NULL");
    return NULL;
  }

  // Generate timestamp for filename
  time_t now = time(NULL);
  struct tm *tm_info = localtime(&now);
  char timestamp[20];
  strftime(timestamp, sizeof(timestamp), "%Y%m%d-%H%M%S", tm_info);

  // Create query file
  char query_filename[256];
  char answer_filename[256];
  snprintf(query_filename, sizeof(query_filename), "/home/<USER>/querydemog/%s.qry", timestamp);
  snprintf(answer_filename, sizeof(answer_filename), "/home/<USER>/querydemog/%s.answer", timestamp);

  log_info("Creating query file for QueryID: %s -> %s", query_id, query_filename);

  // Write query ID to file
  FILE *query_file = fopen(query_filename, "w");
  if (!query_file) {
    log_error("Failed to create query file: %s", query_filename);
    return NULL;
  }
  fprintf(query_file, "queryid=%s\n", query_id);
  fprintf(query_file, "type=auto\n");
  fclose(query_file);

  log_info("Query file created, waiting for answer file: %s", answer_filename);

  // Wait for answer file (timeout after 30 seconds)
  int timeout = 30;
  while (timeout > 0) {
    if (access(answer_filename, F_OK) == 0) {
      log_info("Answer file found: %s", answer_filename);
      break;
    }
    sleep(1);
    timeout--;
  }

  if (timeout == 0) {
    log_error("Timeout waiting for answer file: %s", answer_filename);
    unlink(query_filename);
    return NULL;
  }

  // Read answer file and parse patient data
  FILE *answer_file = fopen(answer_filename, "r");
  if (!answer_file) {
    log_error("Failed to open answer file: %s", answer_filename);
    unlink(query_filename);
    return NULL;
  }

  PatientData *patient_data = malloc(sizeof(PatientData));
  if (!patient_data) {
    log_error("Failed to allocate memory for patient data");
    fclose(answer_file);
    unlink(query_filename);
    unlink(answer_filename);
    return NULL;
  }
  memset(patient_data, 0, sizeof(PatientData));

  log_info("Patient data retrieved:");
  char buffer[1024];
  while (fgets(buffer, sizeof(buffer), answer_file) != NULL) {
    // Remove trailing newline if present
    size_t len = strlen(buffer);
    if (len > 0 && buffer[len-1] == '\n') {
      buffer[len-1] = '\0';
    }
    log_info("  %s", buffer);

    // Parse key=value pairs
    char *equals = strchr(buffer, '=');
    if (equals) {
      *equals = '\0';
      char *key = buffer;
      char *value = equals + 1;
      
      if (strcmp(key, "queryid") == 0) {
        patient_data->query_id = strdup(value);
      } else if (strcmp(key, "status") == 0) {
        patient_data->status = strdup(value);
      } else if (strcmp(key, "temps_ms") == 0) {
        patient_data->temps_ms = strdup(value);
      } else if (strcmp(key, "civilite") == 0) {
        patient_data->civilite = strdup(value);
      } else if (strcmp(key, "nom") == 0) {
        patient_data->nom = strdup(value);
      } else if (strcmp(key, "prenom") == 0) {
        patient_data->prenom = strdup(value);
      } else if (strcmp(key, "date_naissance") == 0) {
        patient_data->date_naissance = strdup(value);
      } else if (strcmp(key, "sexe") == 0) {
        patient_data->sexe = strdup(value);
      }
    }
  }
  fclose(answer_file);

  // Clean up files
  unlink(query_filename);
  unlink(answer_filename);

  log_info("Patient data retrieval completed successfully");
  return patient_data;
}

Message *generate_query_response(const Message *query_message, const PatientData *patient_data,
                                const char *sending_application, const char *sending_facility) {
  log_debug("generate_query_response: Starting query response generation");
  
  const Segment *sender_msh_segment = hl7_message_get_segment_by_type(query_message, SegmentTypeMSH);
  const Separators *separators = &query_message->separators;

  if (!sender_msh_segment) {
    last_error_set("can't generate query response: no MSH segment in sender message");
    return NULL;
  }

  // Create response message FIRST
  log_debug("generate_query_response: Creating response message");
  Message *response = hl7_message_create();
  if (!response) {
    last_error_set("Failed to create response message");
    return NULL;
  }

  // MSH segment for response
  log_debug("generate_query_response: Creating MSH segment for response");
  Segment *response_msh = hl7_segment_create();
  if (!response_msh) {
    hl7_message_destruct(response);
    return NULL;
  }
  
  hl7_segment_set_type(response_msh, SegmentTypeMSH);

  if (hl7_segment_copy_field(response_msh, MSHFieldSeparator, sender_msh_segment, MSHFieldSeparator) ||
      hl7_segment_copy_field(response_msh, MSHEncodingCharacters, sender_msh_segment, MSHEncodingCharacters) ||
      hl7_segment_set_field_string(response_msh, sending_application, separators, MSHSendingApplication) ||
      hl7_segment_set_field_string(response_msh, sending_facility, separators, MSHSendingFacility) ||
      hl7_segment_copy_field(response_msh, MSHReceivingApplication, sender_msh_segment, MSHSendingApplication) ||
      hl7_segment_copy_field(response_msh, MSHReceivingFacility, sender_msh_segment, MSHSendingFacility) ||
      hl7_segment_set_field_string(response_msh, hl7_generate_date_time(), separators, MSHDateTimeOfMessage) ||
      hl7_segment_set_field_string(response_msh, "ADR^A19^ADR_A19", separators, MSHMessageType) ||
      hl7_segment_set_field_string(response_msh, hl7_generate_control_id(), separators, MSHMessageControlID) ||
      hl7_segment_set_field_string(response_msh, "P", separators, MSHProcessingID) ||
#if HL7_VERSION == 260
      hl7_segment_set_field_string(response_msh, "2.6", separators, MSHVersionID)
#elif HL7_VERSION == 250
      hl7_segment_set_field_string(response_msh, "2.5", separators, MSHVersionID)
#elif HL7_VERSION == 240
      hl7_segment_set_field_string(response_msh, "2.4", separators, MSHVersionID)
#endif
  ) {
    log_error("generate_query_response: Failed to set MSH fields for response");
    hl7_segment_destruct(response_msh);
    hl7_message_destruct(response);
    return NULL;
  }

  // Add MSH segment to message IMMEDIATELY after creation
  log_debug("generate_query_response: Adding MSH segment to message");
  if (hl7_message_add_segment(response, response_msh)) {
    log_error("generate_query_response: Failed to add MSH segment");
    hl7_segment_destruct(response_msh);
    hl7_message_destruct(response);
    return NULL;
  }
  // DO NOT free response_msh here - it's now owned by the message

  // MSA segment
  log_debug("generate_query_response: Creating MSA segment for response");
  Segment *response_msa = hl7_segment_create();
  if (!response_msa) {
    hl7_message_destruct(response);
    return NULL;
  }
  
  hl7_segment_set_type(response_msa, SegmentTypeMSA);
  
  const Field *control_id_field = hl7_segment_get_field(sender_msh_segment, MSHMessageControlID);
  char *control_id_str = hl7_field_to_string(control_id_field, separators);
  
  if (hl7_segment_set_field_string(response_msa, "AA", separators, MSAAcknowledgmentCode) ||
      hl7_segment_set_field_string(response_msa, control_id_str ? control_id_str : "", separators, MSAMessageControlID)) {
    hl7_segment_destruct(response_msa);
    hl7_message_destruct(response);
    if (control_id_str) free(control_id_str);
    return NULL;
  }
  if (control_id_str) free(control_id_str);

  // Add MSA segment to message
  log_debug("generate_query_response: Adding MSA segment to message");
  if (hl7_message_add_segment(response, response_msa)) {
    log_error("generate_query_response: Failed to add MSA segment");
    hl7_segment_destruct(response_msa);
    hl7_message_destruct(response);
    return NULL;
  }
  // DO NOT free response_msa here - it's now owned by the message

  // PID segment with patient data
  log_debug("generate_query_response: Creating PID segment for response");
  Segment *pid_segment = hl7_segment_create();
  if (!pid_segment) {
    hl7_message_destruct(response);
    return NULL;
  }
  
  hl7_segment_set_type(pid_segment, SegmentTypePID);
  
  // Format patient name (nom^prenom)
  log_debug("generate_query_response: Formatting patient name");
  char patient_name[512];
  snprintf(patient_name, sizeof(patient_name), "%s^%s", 
           patient_data->nom ? patient_data->nom : "",
           patient_data->prenom ? patient_data->prenom : "");
  
  // Convert date format from DD/MM/YYYY to YYYYMMDD
  log_debug("generate_query_response: Converting birth date format");
  char birth_date[9] = "";
  if (patient_data->date_naissance) {
    int day, month, year;
    if (sscanf(patient_data->date_naissance, "%d/%d/%d", &day, &month, &year) == 3) {
      snprintf(birth_date, sizeof(birth_date), "%04d%02d%02d", year, month, day);
    }
  }
  
  log_debug("generate_query_response: Setting PID fields");
  if (hl7_segment_set_field_string(pid_segment, "1", separators, PIDSetID) ||
      hl7_segment_set_field_string(pid_segment, patient_data->query_id ? patient_data->query_id : "", separators, PIDPatientID) ||
      hl7_segment_set_field_string(pid_segment, patient_name, separators, PIDPatientName) ||
      hl7_segment_set_field_string(pid_segment, birth_date, separators, PIDDateTimeOfBirth) ||
      hl7_segment_set_field_string(pid_segment, patient_data->sexe ? patient_data->sexe : "", separators, PIDAdministrativeSex)) {
    log_error("generate_query_response: Failed to set PID fields for response");
    hl7_segment_destruct(pid_segment);
    hl7_message_destruct(response);
    return NULL;
  }

  // Add PID segment to message
  log_debug("generate_query_response: Adding PID segment to message");
  if (hl7_message_add_segment(response, pid_segment)) {
    log_error("generate_query_response: Failed to add PID segment");
    hl7_segment_destruct(pid_segment);
    hl7_message_destruct(response);
    return NULL;
  }
  // DO NOT free pid_segment here - it's now owned by the message

  // Verify message structure
  size_t segment_count = hl7_message_get_segments_count(response);
  log_debug("generate_query_response: Total segments in response message: %zu", segment_count);
  
  // REMOVE debug calls to hl7_message_to_string here to avoid interference
  
  log_debug("generate_query_response: Query response generated successfully");
  return response;
}

static int send_query_response(const Message *query_message, const PatientData *patient_data) {
  int status = 0;
  BinaryBuffer *bb_tmp = NULL;
  Message *response = NULL;
  unsigned char *bytes = NULL;
  char *response_str = NULL;
  BinaryBufferDocument **docs = NULL;

  log_info("send_query_response: Constructing query response...");

  // Vérification des paramètres d'entrée
  if (!query_message) {
    log_error("send_query_response: query_message is NULL");
    return 1;
  }
  
  if (!patient_data) {
    log_error("send_query_response: patient_data is NULL");
    return 1;
  }
  
  if (!config) {
    log_error("send_query_response: config is NULL");
    return 1;
  }

  // build response message
  log_debug("send_query_response: Calling generate_query_response...");
  response = generate_query_response(query_message, patient_data, 
                                   config->sending_application, config->sending_facility);
  if (!response) {
    log_error("generate_query_response failed");
    status = 1;
    goto send_query_response_clean;
  }
  log_debug("send_query_response: generate_query_response succeeded");

  // Vérification finale de la structure du message avant conversion
  size_t final_segment_count = hl7_message_get_segments_count(response);
  log_debug("send_query_response: Message has %zu segments before string conversion", final_segment_count);

  // AJOUT DE DEBUG : Vérifier le message juste avant conversion
  log_debug("send_query_response: About to call hl7_message_to_string, response pointer: %p", (void*)response);

  // convert to string
  log_debug("send_query_response: Converting response to string...");
  response_str = hl7_message_to_string(response);
  
  // AJOUT DE DEBUG : Vérifier immédiatement après conversion
  log_debug("send_query_response: hl7_message_to_string returned, response_str pointer: %p", (void*)response_str);
  if (response_str) {
    // Debug output - create a safe copy for display
    size_t msg_len = strlen(response_str);
    log_debug("send_query_response: Final message length: %zu", msg_len);
    // Debug_display_str will have \r replaced by \n for readability otherwise the logger should truncate the data becauee of \r
    char *debug_display_str = malloc(msg_len + 1);
    if (debug_display_str) {
        strcpy(debug_display_str, response_str);
        log_debug("send_query_response: Creating debug copy of message string. copy message lenght: %zu", strlen(debug_display_str));
        for (size_t i = 0; i < msg_len; i++) {
            if (debug_display_str[i] == '\r') {
                debug_display_str[i] = '\n';
            }
        }
        log_debug("send_query_response: Final message: \n %s", debug_display_str);
        free(debug_display_str);
    }
  }
  
  if (!response_str) {
    log_error("send_query_response: hl7_message_to_string failed");
    last_error_display(LOG_ERROR, "String conversion failed");
    status = 1;
    goto send_query_response_clean;
  }
  log_debug("send_query_response: Response string conversion succeeded");
  log_info("send_query_response: sending query response...");

  // create binary buffer
  log_debug("send_query_response: Creating binary buffer from response string...");
  size_t str_len = strlen(response_str);
  size_t bytes_count = str_len + 4;
  LOG_CHECK_MALLOC(bytes, malloc(bytes_count));
  LOG_CHECK_MALLOC(bb_tmp, bb_from_string(response_str, bytes, bytes_count));

  // send to transport layer
  log_debug("send_query_response: Converting binary buffer to documents...");
  docs = bb_to_documents_try(bb_tmp);
  if (!docs || !docs[0] || docs[1]) {
    log_error("send_query_response: Expected exactly one document, but got %s", 
              !docs ? "none" : (!docs[0] ? "zero" : "multiple"));
    status = 1;
    goto send_query_response_clean;
  }
  
  size_t length;
  log_debug("send_query_response: Converting document to bytes...");
  const unsigned char *buffer_bytes = bb_document_to_bytes(bb_tmp, docs[0], &length);
  if (!buffer_bytes) {
    log_error("send_query_response: Failed to convert document to bytes");
    status = 1;
    goto send_query_response_clean;
  }
  
  ApplicationToTransportData data = {.bytes = buffer_bytes, .length = length};
  log_debug("send_query_response: Publishing message to transport layer (length: %zu)", length);
  publish_message(application2transport, &data);
  log_debug("send_query_response: Message published successfully");

send_query_response_clean:
  if (bb_tmp)
    bb_destruct(bb_tmp);
  if (bytes)
    free(bytes);
  if (response_str)
    free(response_str);
  if (response)
    hl7_message_destruct(response);
  if (docs) {
    for (BinaryBufferDocument **ptr = docs; *ptr; ptr++)
      free(*ptr);
    free(docs);
  }

  return status;
}

void process_message(const BinaryBufferDocument *doc) {
  Message *message = NULL;
  const char *ts = hl7_generate_date_time();

  log_debug("process_message: Starting message processing");

  // binary storage
  store_binary(doc, ts);

  // textual storage
  store_textual(doc, ts);

  const char *str = bb_document_to_string(bb, doc);

  // parsing
  log_start(LOG_DEBUG, "parsing message...");
  log_debug("Raw HL7 message received (length: %zu):\n%s", strlen(str), str);
  log_debug("Message starts with: %.50s", str);
  log_debug("Message ends with: %s", str + (strlen(str) > 50 ? strlen(str) - 50 : 0));
  message = hl7_message_from_string(str);
  if (!message) {
    last_error_display(LOG_WARN, "message can't be parsed");
    goto process_message_clean;
  }

  // Add diagnostic logging to check message structure
  size_t segment_count = hl7_message_get_segments_count(message);
  log_debug("Message parsed successfully. Total segments found: %zu", segment_count);
  
  // Log all segments found in the message
  for (size_t i = 0; i < segment_count; i++) {
    const Segment *seg = hl7_message_get_segment_by_index(message, i);
    if (seg) {
      SegmentType seg_type = hl7_segment_get_type(seg);
      log_debug("Segment %zu: type = %d (%s)", i, seg_type, hl7_segment_type2str(seg_type));
    }
  }
  log_end(LOG_DEBUG, "done.");

  // Check if this is a query message
  log_debug("Looking for MSH segment in message...");
  const Segment *msh_segment = hl7_message_get_segment_by_type(message, SegmentTypeMSH);
  log_debug("MSH segment found: %s", msh_segment ? "YES" : "NO");
  if (msh_segment) {
    log_debug("Extracting message type from MSH segment...");
    const Field *message_type = hl7_segment_get_field(msh_segment, MSHMessageType);
    if (message_type) {
      char *message_type_str = hl7_field_to_string(message_type, &message->separators);
      log_debug("Message type detected: %s", message_type_str ? message_type_str : "NULL");
      if (message_type_str && strncmp(message_type_str, "QRY^", 4) == 0) {
        log_end(LOG_DEBUG, "parsing completed - query message detected.");
        
        char *query_id_str = NULL; // Déclarer ici pour tout le bloc
        
        // Extract Query ID from QRD segment
        log_debug("Looking for QRD segment in message...");
        const Segment *qrd_segment = hl7_message_get_segment_by_type(message, SegmentTypeQRD);
        log_debug("QRD segment found: %s", qrd_segment ? "YES" : "NO");
        if (qrd_segment) {
          log_debug("Extracting Query ID from QRD segment...");
          const Field *query_id_field = hl7_segment_get_field(qrd_segment, QRDQueryID);
          log_debug("Query ID field found: %s", query_id_field ? "YES" : "NO");
          if (query_id_field) {
            query_id_str = hl7_field_to_string(query_id_field, &message->separators);
            log_debug("Query ID extracted: %s", query_id_str ? query_id_str : "NULL");
            if (query_id_str) {
              log_info("Receving query message for QueryID : %s", query_id_str);
            } else {
              log_info("Receving query message without QueryID");
            }
          } else {
            log_info("Receving query message without fields");
          }
        } else {
          log_info("Receving query message QRY^ (no QRD segment found)");
        }
        
        // CORRECTION : Libérer message_type_str et le mettre à NULL pour éviter le double free
        free(message_type_str);
        message_type_str = NULL;
        
        // Maintenant query_id_str est accessible ici
        if (query_id_str) {
          log_info("Launching patient data retrieval for QueryID: %s", query_id_str);
          PatientData *patient_data = get_patient_data(query_id_str);
          if (patient_data) {
            log_info("Patient data retrieval completed successfully");
            
            // Send query response with patient data
            if (send_query_response(message, patient_data) == 0) {
              log_info("Query response sent successfully");
            } else {
              log_error("Failed to send query response");
            }
            
            patient_data_free(patient_data);
          } else {
            log_error("Patient data retrieval failed");
            // Send acknowledgment even if query failed
            send_acknowledgment(message, false);
          }
          free(query_id_str);
        } else {
          // Send acknowledgment for query without ID
          send_acknowledgment(message, true);
        }
        
        goto process_message_clean;
      }
      // CORRECTION : Vérifier que message_type_str n'est pas NULL avant de libérer
      if (message_type_str) {
        free(message_type_str);
      }
    }
  }
  log_end(LOG_DEBUG, "done.");

  // validation
  char *error_message = hl7_message_is_valid(message);
  if (!error_message) {
    last_error_display(LOG_ERROR, "couldn't run validation on message");
  } else if (strcmp(error_message, "") == 0) {
    log_info("message follows HL7 %s standard", HL7_VERSION);
  } else {
    log_warn("message doesn't follow HL7 %d standard :\n%s", HL7_VERSION,
             error_message);
  }
  if (error_message)
    free(error_message);

  // acknowledgment
  if (config->delay_before_ack != 0) {
    log_start(LOG_DEBUG, "b-waiting %ld seconds before sending acknowledgment...",
              config->delay_before_ack);
    sleep(config->delay_before_ack);
    log_end(LOG_DEBUG, "done.");
  }
  send_acknowledgment(message, true);

process_message_clean:
  hl7_message_destruct(message);
  bb_clean(bb);
}

void application_layer_process_message(void *void_context,
                                       const void *void_data) {
  (void)void_context;
  const TransportToApplicationData *data =
      (TransportToApplicationData *)void_data;

  // invalid length
  if (data->length == 0) {
    log_warn("received zero-length message");
    return;
  }

  bb_append(bb, data->bytes, data->length);
  BinaryBufferDocument **docs = bb_to_documents_try(bb);

  if (docs) {
    for (BinaryBufferDocument **ptr = docs; *ptr; ptr++) {
      process_message(*ptr);
      free(*ptr);
    }

    free(docs);
  } else
    last_error_display(LOG_WARN, "can't read binary buffer");
}

void application_layer_end() { bb_destruct(bb); }


