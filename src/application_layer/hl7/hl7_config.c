#include "hl7_config.h"

#include "log.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024

void application_configuration_init(ApplicationConfiguration *config) {
  config->sending_application = NULL;
  config->sending_facility = NULL;
  config->delay_before_ack = -1;
}

#define FREE_AND_SET_NULL(attribute)                                           \
  if (attribute) {                                                             \
    free(attribute);                                                           \
    attribute = NULL;                                                          \
  }

void application_configuration_destruct(ApplicationConfiguration *config) {
  if (!config)
    return;

  FREE_AND_SET_NULL(config->sending_application);
  FREE_AND_SET_NULL(config->sending_facility);
}

char *
application_configuration_to_string(const ApplicationConfiguration *config) {
  // Allocate a buffer to store the resulting string
  char *result;
  LOG_CHECK_MALLOC(result, malloc(BUFFER_SIZE));

  // Initialize the string
  snprintf(result, BUFFER_SIZE,
           "Configuration:\n"
           "  sending_application: %s\n"
           "  sending_facility: %s\n"
           "  delay_before_ack: %ld\n",
           !config->sending_application ? "NULL" : config->sending_application,
           !config->sending_facility ? "NULL" : config->sending_facility,
           config->delay_before_ack);

  return result;
}
