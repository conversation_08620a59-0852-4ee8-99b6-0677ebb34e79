#include "poct_utils.h"

#include "log.h"
#include <stdlib.h>
#include <string.h>

const char *generate_control_id() {
  static size_t control_id = 0;
  static char control_id_str[30];
  snprintf(control_id_str, sizeof(control_id_str), "%zu", control_id);
  control_id++;
  return control_id_str;
}

xmlDocPtr generate_document_with_header(const char *message_name) {
  // Create a new XML document with root node <ACK.R01>
  xmlDocPtr doc = xmlNewDoc(NULL);

  xmlNodePtr root_node = xmlNewNode(NULL, BAD_CAST message_name);
  xmlDocSetRootElement(doc, root_node);

  // HDR
  xmlNodePtr hdr_node = xmlNewChild(root_node, NULL, BAD_CAST "HDR", NULL);

  xmlNodePtr hdr_control_id =
      xmlNewChild(hdr_node, NULL, BAD_CAST "HDR.control_id", NULL);
  xmlNewProp(hdr_control_id, BAD_CAST "V", BAD_CAST generate_control_id());

  xmlNodePtr hdr_version_id =
      xmlNewChild(hdr_node, NULL, BAD_CAST "HDR.version_id", NULL);
  xmlNewProp(hdr_version_id, BAD_CAST "V", BAD_CAST "POCT1");

  xmlNodePtr hdr_creation_dttm =
      xmlNewChild(hdr_node, NULL, BAD_CAST "HDR.creation_dttm", NULL);
  xmlNewProp(hdr_creation_dttm, BAD_CAST "V",
             BAD_CAST poct_generate_iso8601_timestamp());

  return doc;
}

xmlNodePtr find_direct_child(xmlNodePtr parent, const char *child_name) {
  if (!parent || !child_name)
    return NULL;

  xmlNodePtr child = parent->children;

  // iterate over all children of the parent node
  while (child) {
    if (child->type == XML_ELEMENT_NODE &&
        xmlStrcmp(child->name, (const xmlChar *)child_name) == 0) {
      return child;
    }
    child = child->next;
  }

  return NULL;
}

xmlNodePtr *find_direct_children(xmlNodePtr parent, const char *child_name) {
  if (!parent || !child_name)
    return NULL;

  xmlNodePtr child = parent->children;
  size_t count = 0;
  xmlNodePtr *children = NULL;

  // First pass: count the number of matching children
  while (child) {
    if (child->type == XML_ELEMENT_NODE &&
        xmlStrcmp(child->name, (const xmlChar *)child_name) == 0) {
      count++;
    }
    child = child->next;
  }

  if (count == 0)
    return NULL; // No matching children

  // Allocate memory to store pointers to matching children
  LOG_CHECK_MALLOC(
      children,
      malloc((count + 1) * sizeof(xmlNodePtr))); // +1 for NULL terminator

  // Second pass: store the matching children in the array
  child = parent->children;
  size_t index = 0;
  while (child) {
    if (child->type == XML_ELEMENT_NODE &&
        xmlStrcmp(child->name, (const xmlChar *)child_name) == 0) {
      children[index++] = child;
    }
    child = child->next;
  }

  children[index] = NULL;

  return children;
}

static char **split_path(const char *path, int *count) {
  char *path_copy = strdup(path); // Make a copy of the path
  char *token = strtok(path_copy, "/");
  char **tokens;
  LOG_CHECK_MALLOC(tokens, malloc(sizeof(char *) * strlen(path)));
  *count = 0;

  while (token) {
    tokens[(*count)++] = strdup(token);
    token = strtok(NULL, "/");
  }

  free(path_copy); // free the temporary copy of the path
  return tokens;
}

static void free_tokens(char **tokens, int count) {
  for (int i = 0; i < count; i++)
    free(tokens[i]);
  free(tokens);
}

// recursive function to find a node by following the path
static xmlNodePtr find_node_by_path(xmlNodePtr parent, char **tokens, int index,
                                    int count) {
  if (index >= count || !parent)
    return parent;

  xmlNodePtr child = find_direct_child(parent, tokens[index]);
  return find_node_by_path(child, tokens, index + 1, count);
}

xmlNodePtr get_node_by_path(xmlNodePtr root, const char *path) {
  int token_count;
  char **tokens = split_path(path, &token_count);
  xmlNodePtr node = find_node_by_path(root, tokens, 0, token_count);
  if (!node)
    last_error_set("can't find node with path %s in xml tree", path);
  free_tokens(tokens, token_count);

  return node;
}

char *get_attribute_value_by_path(xmlNodePtr root, const char *path,
                                  const char *attr_name) {
  xmlNodePtr node = get_node_by_path(root, path);
  if (!node)
    return NULL;

  char *attr_value = NULL;
  xmlChar *value = xmlGetProp(node, (const xmlChar *)attr_name);
  if (value) {
    attr_value = strdup((char *)value);
    xmlFree(value);
  } else {
    last_error_set("node with path %s was found, but it has no attribute %s",
                   path, attr_name);
  }

  return attr_value;
}

POCTMessage **generate_response_list(size_t count, ...) {
  if (count <= 0)
    return NULL;

  int status = 0;
  size_t processed = 0;

  va_list args;
  va_start(args, count);

  POCTMessage **response_list;
  LOG_CHECK_MALLOC(response_list, malloc((count + 1) * sizeof(POCTMessage *)));

  for (size_t i = 0; i < count + 1; ++i)
    response_list[i] = NULL;

  for (processed = 0; processed < count; ++processed) {
    POCTMessage *message = va_arg(args, POCTMessage *);

    // if any argument is NULL, free the list and return an empty list
    if (!message) {
      status = 1;
      goto generate_response_list_clean;
    }

    response_list[processed] = message;
  }

generate_response_list_clean:
  if (status) {
    for (size_t i = 0; i < processed; i++)
      poct_message_destruct(response_list[i]);
    for (size_t i = processed + 1; i < count; i++) {
      POCTMessage *message = va_arg(args, POCTMessage *);
      if (message)
        poct_message_destruct(message);
    }

    if (response_list)
      free(response_list);
    response_list = NULL;
  }
  va_end(args);

  return response_list;
}

// Function to create or find a node by following the path
xmlNodePtr create_or_find_node(xmlNodePtr parent, char **tokens, int index,
                               int count) {
  if (index >= count)
    return parent;

  xmlNodePtr child =
      xmlHasProp(parent, (const xmlChar *)tokens[index])
          ? xmlFirstElementChild(parent)
          : xmlNewChild(parent, NULL, (const xmlChar *)tokens[index], NULL);

  return create_or_find_node(child, tokens, index + 1, count);
}

// Function to generate an XML document with arbitrary children nodes
POCTMessage *generate_document_with_children(const char *message_name,
                                             const PoctSpecification *spec,
                                             int num_children, ...) {
  // Create the base document with a header
  xmlDocPtr doc = generate_document_with_header(message_name);
  xmlNodePtr root = xmlDocGetRootElement(doc); // Get root element

  va_list args;
  va_start(args, num_children);

  // Loop over each path and attribute pair
  for (int i = 0; i < num_children; i++) {
    const char *path = va_arg(args, const char *);
    const char *attr_name = va_arg(args, const char *);
    const char *attr_value = va_arg(args, const char *);

    // Split the path into node names
    int token_count;
    char **tokens = split_path(path, &token_count);

    // Create or find the node by following the path
    xmlNodePtr node = create_or_find_node(root, tokens, 0, token_count);

    // Set the attribute on the node
    xmlNewProp(node, BAD_CAST attr_name, BAD_CAST attr_value);

    // Free the tokens for the current path
    free_tokens(tokens, token_count);
  }

  va_end(args);

  return poct_message_from_xml_doc(doc, spec);
}

POCTMessage *generate_acknowledgment(bool success,
                                     const POCTMessage *acknowledged_message,
                                     const PoctSpecification *spec) {
  static char control_id_str[30];
  snprintf(control_id_str, 30, "%d", acknowledged_message->control_id);

  return generate_document_with_children(
      "ACK.R01", spec, 2, "ACK/ACK.type_cd", "V", success ? "AA" : "AE",
      "ACK/ACK.ack_control_id", "V", control_id_str);
}
