#include "hb201dm.h"

#include "log.h"
#include "poct_utils.h"
#include "poct_standard_handlers.h"
#include "poct_layer.h"
#include <libxml/parser.h>
#include <libxml/tree.h>
#include <stdbool.h>
#include <stdlib.h>

/////////////////////////////////////////
// MESSAGES AND TOPICS
/////////////////////////////////////////

enum {
  P<PERSON>TTop<PERSON><PERSON><PERSON>,
  POCTTopicStatus,
  POCTTopicObservations,
  POCTTopicGoodbye,
};

enum {
  POCTMessageHello,
  POCTMessageAcknowledge,
  POCTMessageStatus,
  POCTMessageRequest,
  POCTMessagePatientObservation,
  POCTMessageQualityControlObservation,
  POCTMessageEvents,
  POCTMessageEndOfTopic,
  POC<PERSON>essagePatientList,      
  POCTMessagePatientListItem,
};

static const POCTTopicType topics[] = {
    POCTTopic<PERSON>ello,
    POCTTopicStatus,
    POCTTopicObservations,
    POCTTopicGoodbye,
};

static const char *topics_names[] = {
    "",
    "",
    "",
    "",
};

static const POCTTopicType messages[] = {POCTMessageHello,
                                         POCTMessageAcknowledge,
                                         POCTMessageStatus,
                                         POCTMessageRequest,
                                         POCTMessagePatientObservation,
                                         POCTMessageQualityControlObservation,
                                         POCTMessageEvents,
                                         POCTMessageEndOfTopic,
                                         POCTMessageRequest,
                                         POCTMessagePatientList,
                                         POCTMessagePatientListItem};

static const char *messages_names[] = {"HEL.R01", "ACK.R01", "DST.R01",
                                       "REQ.R01", "OBS.R01", "OBS.R02",
                                       "EVS",     "EOT.R01", "DTV.R01",
                                       "PTL.R01", "PTL.R02"};

const PoctSpecification poct_specification = {.messages = messages,
                                              .messages_length = 11,
                                              .messages_strings =
                                                  messages_names,
                                              .topics = topics,
                                              .topics_length = 4,
                                              .topics_strings = topics_names};

/////////////////////////////////////////
// CONTEXT
/////////////////////////////////////////

typedef enum {
  StepInit,
  StepStatus,
  StepObservations,
  StepEvents,
} Step;

typedef struct ContextStruct {
  Step current_step;
  char *current_conversation;
  char *vendor_id;
  char *device_id;
  size_t observations_to_retrieve;
  size_t events_to_retrieve;
  size_t message_count;
} Context;

static Context *context_create() {
  Context *context;
  LOG_CHECK_MALLOC(context, malloc(sizeof(Context)));

  context->current_step = StepInit;
  context->current_conversation = NULL;
  context->vendor_id = NULL;
  context->device_id = NULL;
  context->message_count = 0;
  context->observations_to_retrieve = 0;
  context->events_to_retrieve = 0;

  return context;
}

static void context_destruct(Context *context) {
  if (!context)
    return;

  if (context->current_conversation)
    free(context->current_conversation);

  if (context->vendor_id)
    free(context->vendor_id);

  if (context->device_id)
    free(context->device_id);

  free(context);
}

// Reset the context to its initial state.
static void context_reset(Context *context) {
  
  // Free the current conversation if it exists and set the pointer to NULL.
  if (context->current_conversation) {
    free(context->current_conversation);
    context->current_conversation = NULL;
  }

  // Reset the current step.
  context->current_step = StepInit;

  // Free device_id if it exists.
  if (context->device_id) {
    free(context->device_id);
    context->device_id = NULL; // It's a good practice to also set this to NULL.
  }

  // Free vendor_id if it exists.
  if (context->vendor_id) {
    free(context->vendor_id);
    context->vendor_id = NULL; // And this one too.
  }
}

const char *poct_conversation_name(POCTConversationHandler *pch) {
  Context *context = (Context *)pch->context;
  if (!context->current_conversation)
    context->current_conversation = strdup(poct_generate_iso8601_timestamp());

  return context->current_conversation;
}

const char *poct_message_name(POCTConversationHandler *pch) {
  Context *context = (Context *)pch->context;
  static char name[10];
  snprintf(name, 10, "%zu", context->message_count);

  return name;
}

/////////////////////////////////////////
// HANDLERS
/////////////////////////////////////////

// helpers

static void stop_conversation_on_error(const POCTMessage *received_message,
                                       Context *context,
                                       POCTMessageHandlingResult *result) {

  context_reset(context);
  result->response = generate_response_list(
      1, generate_acknowledgment(false, received_message, &poct_specification));
}

#define NEED_NODE(root, path, goto_flag)                                       \
  if (!get_node_by_path(root, path)) {                                         \
    last_error_display(LOG_WARN, "xml node not found");                        \
    status = 1;                                                                \
    goto goto_flag;                                                            \
  }

#define NEED_ATTRIBUTE(root, path, attr_name, variable, goto_flag)             \
  variable = NULL;                                                             \
  variable = get_attribute_value_by_path(root, path, attr_name);               \
  if (!variable) {                                                             \
    last_error_display(LOG_WARN, "xml attribute not found");                   \
    status = 1;                                                                \
    goto goto_flag;                                                            \
  }

// hello

static void hello_handler(const POCTMessage *message, Context *context,
                          POCTMessageHandlingResult *result) {
  int status = 0;

  // needed nodes
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  NEED_ATTRIBUTE(root, "DEV/DEV.device_id", "V", context->device_id,
                 hello_handler_clean);
  NEED_ATTRIBUTE(root, "DEV/DEV.vendor_id", "V", context->vendor_id,
                 hello_handler_clean);

  log_debug("starting new conversation with device id %s, vendor id %s",
            context->device_id, context->vendor_id);

  // context
  context->current_step = StepStatus;

  // response
  result->message_is_valid = true;
  result->response = generate_response_list(
      1, generate_acknowledgment(true, message, &poct_specification));

hello_handler_clean:
  if (status)
    stop_conversation_on_error(message, context, result);
}

// status
static void status_handler(const POCTMessage *message, Context *context,
                           POCTMessageHandlingResult *result) {
  int status = 0;
  char *str = NULL; // Initialize str to NULL for safety.

  // needed nodes
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  NEED_ATTRIBUTE(root, "DST/DST.new_observations_qty", "V", str,
                 status_handler_clean);
  context->observations_to_retrieve = atoi(str);
  // Do NOT free str here. The memory will be freed at the end.

  NEED_ATTRIBUTE(root, "DST/DST.new_events_qty", "V", str,
                 status_handler_clean);
  context->events_to_retrieve = atoi(str);
  // Do NOT free str here either.

  log_info("retrieving %zu observations and %zu events",
           context->observations_to_retrieve, context->events_to_retrieve);

  // context
  context->current_step = StepObservations;

  // response
  result->message_is_valid = true;
  result->response = generate_response_list(
      2, generate_acknowledgment(true, message, &poct_specification),
      generate_document_with_children("REQ.R01", &poct_specification, 1,
                                     "REQ/REQ.request_cd", "V", "ROBS"));

status_handler_clean:
  if (status)
    stop_conversation_on_error(message, context, result);
  if (str)
    free(str);
}

// observations

static void observations_handler(const POCTMessage *message, Context *context,
                                 POCTMessageHandlingResult *result) {
  int status = 0;

  // needed nodes
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  char *str;

  if (message->type == POCTMessagePatientObservation ||
      message->type == POCTMessageQualityControlObservation) {
    // continue to receive
    if (message->type == POCTMessagePatientObservation) {
      NEED_ATTRIBUTE(root, "SVC/PT/PT.patient_id", "V", str,
                     observations_handler_clean);
      log_info("received patient observations");
    } else if (message->type == POCTMessageQualityControlObservation) {
      NEED_ATTRIBUTE(root, "SVC/CTC/CTC.name", "V", str,
                     observations_handler_clean);
      log_info("received quality control observations");
    }

    context->current_step = StepObservations;
    result->message_is_valid = true;
    result->response = generate_response_list(
        1, generate_acknowledgment(true, message, &poct_specification));
  } else if (message->type == POCTMessageEndOfTopic) {
    // stop receiving
    context->current_step = StepEvents;
    result->message_is_valid = true;
    result->response = generate_response_list(
        2, generate_acknowledgment(true, message, &poct_specification),
        generate_document_with_children("REQ.R01", &poct_specification, 1,
                                        "REQ/REQ.request_cd", "V", "RDEV"));
  }

observations_handler_clean:
  if (str)
    free(str);
  if (status)
    stop_conversation_on_error(message, context, result);
}

// observations

static void events_handler(const POCTMessage *message, Context *context,
                           POCTMessageHandlingResult *result) {
  int status = 0;

  // needed nodes
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  char *str;

  if (message->type == POCTMessageEvents) {
    // continue to receive
    NEED_ATTRIBUTE(root, "SVC/EVT/EVT.description", "V", str,
                   observations_handler_clean);
    log_info("received events");

    context->current_step = StepEvents;
    result->message_is_valid = true;
    result->response = generate_response_list(
        1, generate_acknowledgment(true, message, &poct_specification));
  } else if (message->type == POCTMessageEndOfTopic) {
    // end of conversation
    result->message_is_valid = true;
    result->response = generate_response_list(
        1, generate_document_with_children("DTV.R01", &poct_specification, 1,
                                           "DTV/DTV.command_cd", "V",
                                           "START_CONTINUOUS"));
    context_reset(context);
  }

observations_handler_clean:
  if (str)
    free(str);
  if (status)
    stop_conversation_on_error(message, context, result);
}

// patient list request handler (PTL.R01)
static void patient_list_request_handler(const POCTMessage *message, Context *context,
                                         POCTMessageHandlingResult *result) {
  (void)context;  // Paramètre non utilisé
  log_info("Processing patient list request (PTL.R01)");

  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);

  // Extraire les informations du header
  char *control_id = get_attribute_value_by_path(root, "HDR/HDR.control_id", "V");
  char *version_id = get_attribute_value_by_path(root, "HDR/HDR.version_id", "V");
  char *creation_time = get_attribute_value_by_path(root, "HDR/HDR.creation_dttm", "V");

  if (control_id) {
    log_debug("Control ID: %s", control_id);
    free(control_id);
  }
  if (version_id) {
    log_debug("Version ID: %s", version_id);
    free(version_id);
  }
  if (creation_time) {
    log_debug("Creation time: %s", creation_time);
    free(creation_time);
  }

  // Compter les patients dans la liste
  xmlNodePtr pt_node = root->children;
  int patient_count = 0;
  while (pt_node) {
    if (xmlStrcmp(pt_node->name, (const xmlChar*)"PT") == 0) {
      patient_count++;
      char *patient_id = get_attribute_value_by_path(pt_node, "PT.patient_id", "V");
      char *patient_name = get_attribute_value_by_path(pt_node, "PT.name", "V");

      if (patient_id && patient_name) {
        log_info("Patient %d: ID=%s, Name=%s", patient_count, patient_id, patient_name);
      }

      if (patient_id) free(patient_id);
      if (patient_name) free(patient_name);
    }
    pt_node = pt_node->next;
  }

  log_info("Received patient list with %d patients", patient_count);

  // Générer une réponse d'acknowledgment
  result->message_is_valid = true;
  result->response = generate_response_list(
      1, generate_acknowledgment(true, message, &poct_specification));
}

// patient list item handler (PTL.R02)
static void patient_list_item_handler(const POCTMessage *message, Context *context,
                                     POCTMessageHandlingResult *result) {
  (void)context;  // Paramètre non utilisé
  log_info("Processing patient list item update (PTL.R02)");

  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);

  // Extraire les informations du header
  char *control_id = get_attribute_value_by_path(root, "HDR/HDR.control_id", "V");
  if (control_id) {
    log_debug("Control ID: %s", control_id);
    free(control_id);
  }

  // Extraire l'action de mise à jour
  char *action_code = get_attribute_value_by_path(root, "UPD/UPD.action_cd", "V");
  if (action_code) {
    log_info("Update action: %s", action_code);

    if (strcmp(action_code, "D") == 0) {
      log_info("Action: Delete patient");
    } else if (strcmp(action_code, "A") == 0) {
      log_info("Action: Add patient");
    } else if (strcmp(action_code, "U") == 0) {
      log_info("Action: Update patient");
    }

    free(action_code);
  }

  // Extraire les informations du patient
  char *patient_id = get_attribute_value_by_path(root, "UPD/PT/PT.patient_id", "V");
  if (patient_id) {
    log_info("Patient ID: %s", patient_id);
    free(patient_id);
  }

  // Générer une réponse d'acknowledgment
  result->message_is_valid = true;
  result->response = generate_response_list(
      1, generate_acknowledgment(true, message, &poct_specification));
}

// main handler

typedef void (*MessageHandler)(const POCTMessage *, Context *,
                               POCTMessageHandlingResult *);

static const MessageHandler handlers[] = {
    [StepInit] = hello_handler,
    [StepStatus] = status_handler,
    [StepObservations] = observations_handler,
    [StepEvents] = events_handler,
};

static const POCTMessageType handlers_expected_messages[][10] = {
    [StepInit] = {POCTMessageHello, POCTMessagePatientList, POCTMessagePatientListItem, -1},
    [StepStatus] = {POCTMessageStatus, POCTMessagePatientList, POCTMessagePatientListItem, -1},
    [StepObservations] = {POCTMessagePatientObservation,
                          POCTMessageQualityControlObservation,
                          POCTMessageEndOfTopic,
                          POCTMessagePatientList, POCTMessagePatientListItem, -1},
    [StepEvents] = {POCTMessageEvents, POCTMessageEndOfTopic,
                    POCTMessagePatientList, POCTMessagePatientListItem, -1}};

static POCTMessageHandlingResult *handler(const POCTMessage *message,
                                          void *context_void) {
  if (!message) {
    log_error("can't handle a NULL message");
    return NULL;
  }

  (void)message;
  Context *context = (Context *)context_void;

  POCTMessageHandlingResult *result =
      (POCTMessageHandlingResult *)malloc(sizeof(POCTMessageHandlingResult));

  result->message_is_valid = false;
  result->conversation_name = context->current_conversation;
  result->response = NULL;

  bool message_has_good_type = false;
  for (const POCTMessageType *ptr =
           handlers_expected_messages[context->current_step];
       *ptr != -1; ptr++) {
    if (*ptr == message->type) {
      message_has_good_type = true;
      break;
    }
  }

  if (!message_has_good_type) {
    log_error("wrong message type");
    stop_conversation_on_error(message, context, result);
    return result;
  }

  // Traiter les messages PTL.R01 et PTL.R02 directement
  if (message->type == POCTMessagePatientList) {
    patient_list_request_handler(message, context, result);
    return result;
  }

  if (message->type == POCTMessagePatientListItem) {
    patient_list_item_handler(message, context, result);
    return result;
  }

  handlers[context->current_step](message, context, result);
  context->message_count++;

  return result;
}

/////////////////////////////////////////
// POCT CONVERSATION HANDLER
/////////////////////////////////////////

POCTConversationHandler *poct_conversation_handler_create() {
  POCTConversationHandler *pch;
  LOG_CHECK_MALLOC(pch, malloc(sizeof(POCTConversationHandler)));

  Context *context = context_create();
  if (!context) {
    poct_conversation_handler_destruct(pch);
    return NULL;
  }
  context->current_conversation = NULL;

  pch->context = context;
  pch->handler = handler;

  return pch;
}

void poct_conversation_handler_destruct(POCTConversationHandler *pch) {
  if (!pch)
    return;

  if (pch->context)
    context_destruct(pch->context);

  free(pch);
}

// Exporter le driver
const POCTDeviceDriver hb201dm_driver = {
    .device_name = "HB201DM",
    .specification = &poct_specification,
    .create_handler = poct_conversation_handler_create,
    .destroy_handler = poct_conversation_handler_destruct
};
