#include "poct_layer.h"

#include "application_to_storing.h"
#include "application_to_transport.h"
#include "binary_buffer.h"
#include "log.h"
#include "poct.h"
#include "poct_config.h"
#include "transport_to_application.h"
#include "application_layer/poct/drivers/hb201dm.h"
#include <libxml2/libxml/parser.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#define BUFFER_SIZE 1024 * 1000

// Macros de sécurité pour la gestion mémoire
#define SAFE_FREE(ptr) do { \
  if (ptr) { \
    free(ptr); \
    ptr = NULL; \
  } \
} while(0)

#define SAFE_BB_DESTRUCT(bb_ptr) do { \
  if (bb_ptr) { \
    bb_destruct(bb_ptr); \
    bb_ptr = NULL; \
  } \
} while(0)

#define SAFE_FREE_DOCS(docs_ptr) do { \
  if (docs_ptr) { \
    for (BinaryBufferDocument **ptr = docs_ptr; *ptr; ptr++) { \
      SAFE_FREE(*ptr); \
    } \
    SAFE_FREE(docs_ptr); \
  } \
} while(0)

// unlike input_output model, this module doesn't use
// dynamic allocationn
// this choice was taken considering embedded systems requirements
// static allocation allows compile-time memory allocation,
// helping to prevent running short of memory at runtime

static unsigned char buffer[BUFFER_SIZE];
BinaryBuffer *bb;

static ApplicationConfiguration *config;
static PubSub *transport2application;
static PubSub *application2transport;
static PubSub *application2storing;
static POCTConversationHandler *pch;

// Global variables for the current driver
static const POCTDeviceDriver *registered_driver = NULL;

int poct_register_device_driver(const POCTDeviceDriver *driver) {
    if (!driver || !driver->specification || !driver->create_handler) {
        log_error("Invalid POCT driver");
        return 1;
    }
    
    registered_driver = driver;
    log_info("Registered POCT driver: %s", driver->device_name);
    return 0;
}

const POCTDeviceDriver *poct_get_current_driver(void) {
    return registered_driver;
}

int application_layer_init(ApplicationConfiguration *init_config,
                           PubSub *init_transport2application,
                           PubSub *init_application2transport,
                           PubSub *init_application2storing) {
  int status = 0;

  config = init_config;
  transport2application = init_transport2application;
  application2transport = init_application2transport;
  application2storing = init_application2storing;

  poct_start();

  bb = bb_create(buffer, BUFFER_SIZE);
  if (!bb) {
    last_error_display(LOG_ERROR, "can't create binary buffer");
    status = 1;
    goto application_layer_init_clean;
  }

  if (add_subscriber(transport2application, application_layer_process_message,
                     NULL)) {
    last_error_display(LOG_ERROR, "can't subscribe");
    status = 1;
    goto application_layer_init_clean;
  }

  // Using registered driver
  if (!registered_driver) {
    last_error_display(LOG_ERROR, "No POCT driver registered");
    return 1;
  }
  
  pch = registered_driver->create_handler();
  if (!pch) {
    status = 1;
    goto application_layer_init_clean;
  }

application_layer_init_clean:
  if (status) {
    SAFE_BB_DESTRUCT(bb);

    if (pch)
      poct_conversation_handler_destruct(pch);
  }

  return status;
}

static void store_binary(const BinaryBufferDocument *doc, const char *ts,
                         const char *conversation_name) {
  size_t length;
  const unsigned char *data = bb_document_to_bytes(bb, doc, &length);
  BinaryFileData data_bin = {.bytes = data, .length = length};
  ApplicationToStoringData storing_data_bin = {.file_data = &data_bin,
                                               .file_type = FT_BINARY,
                                               .folder = conversation_name,
                                               .name = ts};
  publish_message(application2storing, &storing_data_bin);
}

static void store_textual(const BinaryBufferDocument *doc, const char *ts,
                          const char *conversation_name) {
  const char *str = bb_document_to_string(bb, doc);
  TextualFileData data_text = {.message = str};

  ApplicationToStoringData storing_data_txt = {.file_data = &data_text,
                                               .file_type = FT_TEXTUAL,
                                               .folder = conversation_name,
                                               .name = ts};
  publish_message(application2storing, &storing_data_txt);
}

// Sending response
static void send_response(POCTMessage **response) {
  if (!response) {
    log_error("can't send NULL response");
    return;
  }

  unsigned char *buffer = NULL;
  BinaryBuffer *bb_tmp = NULL;
  char *str = NULL;
  BinaryBufferDocument **docs = NULL;
  size_t count = 0;

  for (POCTMessage **response_ptr = response; *response_ptr; response_ptr++) {
    // Nettoyage sécurisé au début de chaque itération
    SAFE_FREE(buffer);
    SAFE_BB_DESTRUCT(bb_tmp);
    SAFE_FREE(str);
    SAFE_FREE_DOCS(docs);

    str = poct_message_to_string(*response_ptr);
    if (!str) {
      continue;
    }

    size_t buffer_len = strlen(str) + 4;
    LOG_CHECK_MALLOC(buffer, malloc(buffer_len));
    if (!buffer) {
      SAFE_FREE(str);
      continue;
    }

    bb_tmp = bb_from_string(str, buffer, buffer_len);
    SAFE_FREE(str);
    
    if (!bb_tmp) {
      last_error_display(LOG_ERROR,
                         "can't create binary buffer for response sending");
      goto send_response_clean;
    }

    docs = bb_to_documents_try(bb_tmp);
    if (!docs || !docs[0] || docs[1]) {
      log_error("one document should have been created");
      SAFE_FREE_DOCS(docs);
      continue;
    }

    // Processing the valid document
    // Convert the document to bytes
    size_t length;
    const unsigned char *bytes = bb_document_to_bytes(bb_tmp, docs[0], &length);
    if (bytes) {
      ApplicationToTransportData data = {.bytes = bytes, .length = length};
      publish_message(application2transport, &data);
      count++;
    }

    // Immediate release after successful use
    SAFE_FREE_DOCS(docs);
  }

send_response_clean:
  // Final secure cleanup
  // Free all allocated resources
  SAFE_BB_DESTRUCT(bb_tmp);
  SAFE_FREE(buffer);
  SAFE_FREE(str);
  SAFE_FREE_DOCS(docs);
}

// Processing the message
void process_message(const BinaryBufferDocument *doc) {
  POCTMessage *message = NULL;
  POCTMessageHandlingResult *handling_result = NULL;
  const char *message_name = poct_message_name(pch);
  const char *conversation_name = poct_conversation_name(pch);

  // binary storage
  store_binary(doc, message_name, conversation_name);

  // textual storage
  store_textual(doc, message_name, conversation_name);

  const char *str = bb_document_to_string(bb, doc);

  // parsing
  log_start(LOG_DEBUG, "parsing document...");
  message = poct_message_from_string(str, registered_driver->specification);
  if (!message) {
    last_error_display(LOG_WARN, "can't create message");
    goto process_message_clean;
  }
  log_end(LOG_DEBUG, "done.");

  // validation
  log_start(LOG_DEBUG, "validating document...");
  handling_result = pch->handler(message, pch->context);
  if (!handling_result)
    goto process_message_clean;

  if (handling_result->message_is_valid) {
    log_end(LOG_DEBUG, "document is valid");

    if (handling_result->response) {
      log_start(LOG_DEBUG,
                "waiting %zu milliseconds before sending response...",
                config->response_timeout_ms);
      usleep(config->response_timeout_ms * 1000);
      log_end(LOG_DEBUG, "done.");
      send_response(handling_result->response);
    } else
      log_debug("no response needed");
  } else {
    log_warn("document is invalid");
  }

process_message_clean:
  poct_message_destruct(message);
  poct_handling_result_destruct(handling_result);
  bb_clean(bb);
}

// Processing the message
void application_layer_process_message(void *void_context,
                                       const void *void_data) {
  (void)void_context;
  const TransportToApplicationData *data =
      (TransportToApplicationData *)void_data;

  // invalid length
  if (data->length == 0) {
    log_warn("received zero-length message");
    return;
  }

  bb_append(bb, data->bytes, data->length);
  BinaryBufferDocument **docs = bb_to_documents_try(bb);

  // TODO: poct handler when binary buffer is dropped
  if (docs) {
    for (BinaryBufferDocument **ptr = docs; *ptr; ptr++) {
      process_message(*ptr);
      SAFE_FREE(*ptr);
    }
    SAFE_FREE(docs);
  } else {
    last_error_display(LOG_WARN, "can't read binary buffer");
  }
}

void application_layer_end() {
  poct_conversation_handler_destruct(pch);
  SAFE_BB_DESTRUCT(bb);
  poct_end();
}