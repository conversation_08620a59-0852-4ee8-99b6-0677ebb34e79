#include "poct_standard_handlers.h"
#include "poct_utils.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>
#include <libxml2/libxml/tree.h>
#include <poct.h>


// Fonction utilitaire pour trouver un type de message par nom
POCTMessageType get_message_type_by_name(const PoctSpecification *spec, const char *name) {
    if (!spec || !name) {
        return -1;
    }
    
    for (size_t i = 0; i < spec->messages_length; i++) {
        if (strcmp(spec->messages_strings[i], name) == 0) {
            return spec->messages[i];
        }
    }
    
    return -1;  // Message non trouvé
}

// Handler générique pour PT_LST (demande de liste de patients)
POCTMessageHandlingResult *handle_patient_list_request(
    const POCTMessage *message,
    void *context,
    const PoctSpecification *spec) {

    (void)context;  // Paramètre non utilisé dans cette implémentation générique

    POCTMessageHandlingResult *result = malloc(sizeof(POCTMessageHandlingResult));
    if (!result) {
        return NULL;
    }
    
    result->message_is_valid = false;
    result->response = NULL;
    result->conversation_name = NULL;
    
    if (!message || message->type != get_message_type_by_name(spec, "PT_LST")) {
        log_error("Invalid PT_LST message");
        return result;
    }
    
    log_info("Processing patient list request");
    
    // Logique générique pour traiter PT_LST
    xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
    char *query_criteria = get_attribute_value_by_path(root, "QRY/QRY.criteria", "V");
    
    if (query_criteria) {
        log_debug("Patient list query criteria: %s", query_criteria);
        free(query_criteria);
    }
    
    result->message_is_valid = true;
    result->response = generate_response_list(
        1, generate_acknowledgment(true, message, spec));
    
    return result;
}

// Handler générique pour PT_LST_I (item de liste de patients)
POCTMessageHandlingResult *handle_patient_list_item(
    const POCTMessage *message,
    void *context,
    const PoctSpecification *spec) {

    (void)context;  // Paramètre non utilisé dans cette implémentation générique

    POCTMessageHandlingResult *result = malloc(sizeof(POCTMessageHandlingResult));
    if (!result) {
        return NULL;
    }
    
    result->message_is_valid = false;
    result->response = NULL;
    result->conversation_name = NULL;
    
    if (!message || message->type != get_message_type_by_name(spec, "PT_LST_I")) {
        log_error("Invalid PT_LST_I message");
        return result;
    }
    
    xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
    char *patient_id = get_attribute_value_by_path(root, "PAT/PAT.patient_id", "V");
    
    if (patient_id) {
        log_info("Processing patient list item for patient: %s", patient_id);
        
        // Ici on peut appeler une fonction générique de récupération de données patient
        // qui sera implémentée de manière agnostique
        
        free(patient_id);
    }
    
    result->message_is_valid = true;
    result->response = generate_response_list(
        1, generate_acknowledgment(true, message, spec));
    
    return result;
}
