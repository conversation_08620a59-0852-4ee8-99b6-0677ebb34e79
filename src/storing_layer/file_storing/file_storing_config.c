#include "file_storing_config.h"

#include "log.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024

void storing_configuration_init(StoringConfiguration *config) {
  config->storing_directory = NULL;
}

#define FREE_AND_SET_NULL(attribute)                                           \
  if (attribute) {                                                             \
    free(attribute);                                                           \
    attribute = NULL;                                                          \
  }

void storing_configuration_destruct(StoringConfiguration *config) {
  if (!config)
    return;

  FREE_AND_SET_NULL(config->storing_directory);
}

char *storing_configuration_to_string(const StoringConfiguration *config) {
  // Allocate a buffer to store the resulting string
  char *result;
  LOG_CHECK_MALLOC(result, malloc(BUFFER_SIZE));

  // Initialize the string
  snprintf(result, BUFFER_SIZE,
           "Configuration:\n"
           "  storing_directory: %s\n",
           !config->storing_directory ? "NULL" : config->storing_directory);

  return result;
}
