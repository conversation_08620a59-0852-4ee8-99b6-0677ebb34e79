#include "file_storing_layer.h"

#include "application_to_storing.h"
#include "directory.h"
#include "log.h"
#include <errno.h>
#include <fcntl.h>
#include <libgen.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

static char home_folder[PATH_MAX_LENGTH];
static char messages_folder[PATH_MAX_LENGTH];
static char current_path[PATH_MAX_LENGTH];

static StoringConfiguration *config;
static PubSub *application2storing;

int storing_layer_init(StoringConfiguration *init_config,
                       PubSub *init_application2storing) {
  config = init_config;
  application2storing = init_application2storing;

  bzero(home_folder, PATH_MAX_LENGTH);
  bzero(messages_folder, PATH_MAX_LENGTH);
  bzero(current_path, PATH_MAX_LENGTH);

  if (config->storing_directory) {
    strncpy(home_folder, config->storing_directory, PATH_MAX_LENGTH - 1);
    home_folder[PATH_MAX_LENGTH - 1] = '\0';
  }

  if (snprintf(messages_folder, PATH_MAX_LENGTH, "%s/%s", home_folder,
               MESSAGES_FOLDER) >= PATH_MAX_LENGTH) {
    log_error("path for storing messages is too long");
    return 1;
  }

  if (directory_is_writable(messages_folder) != 1) {
    last_error_display(LOG_ERROR, "directory %s can't be written to, aborting",
                       messages_folder);
    return 1;
  }

  add_subscriber(application2storing, storing_layer_process_message, NULL);

  log_info("messages will be stored in %s", messages_folder);

  return 0;
}

static void store_message_binary(const BinaryFileData *data) {
  int fd = open(current_path, O_WRONLY | O_CREAT, 0644);
  if (fd == -1) {
    log_error("can't open file on path \"%s\" to store message (errno:%d - %s)",
              current_path, errno, strerror(errno));
    goto ce_store_message_binary;
  }

  const ssize_t n = write(fd, data->bytes, data->length);
  if (n == -1) {
    log_error("error while writing to file \"%s\" (errno:%d - %s)", errno,
              current_path, strerror(errno));
    goto ce_store_message_binary;
  }

ce_store_message_binary:
  if (fd != -1)
    close(fd);
}

static void store_message_textual(const TextualFileData *data) {
  FILE *fd = NULL;
  fd = fopen(current_path, "w");
  if (!fd) {
    log_error("can't open file on path \"%s\" to store message (errno:%d - %s)",
              current_path, errno, strerror(errno));
    goto ce_store_message_textual;
  }

  if ((size_t)fprintf(fd, "%s", data->message) != strlen(data->message)) {
    log_error("error while writing to file \"%s\" (errno:%d - %s)", errno,
              current_path, strerror(errno));
    goto ce_store_message_textual;
  }

ce_store_message_textual:
  if (fd)
    fclose(fd);
}

void storing_layer_process_message(void *void_context, const void *void_data) {
  (void)void_context;
  ApplicationToStoringData *data = (ApplicationToStoringData *)void_data;

  if (snprintf(current_path, PATH_MAX_LENGTH, "%s/%s", messages_folder,
               data->folder) >= PATH_MAX_LENGTH) {
    log_error("folder path is too long");
    return;
  }

  if (!directory_is_writable(current_path)) {
    last_error_display(LOG_ERROR, "can't acess or create directory %s",
                       current_path);
    return;
  }

  const char *extension = file_type_get_extension(data->file_type);

  if (snprintf(current_path, PATH_MAX_LENGTH, "%s/%s/%s.%s", messages_folder,
               data->folder, data->name, extension) >= PATH_MAX_LENGTH) {
    log_error("path for storing data is too long");
    return;
  }

  // for the strange syntax : https://stackoverflow.com/a/18496437/13123535
  switch (data->file_type) {
  case FT_BINARY:;
    const BinaryFileData *file_data_txt = (BinaryFileData *)data->file_data;
    store_message_binary(file_data_txt);
    break;

  case FT_TEXTUAL:;
    const TextualFileData *file_data_bin = (TextualFileData *)data->file_data;
    store_message_textual(file_data_bin);
    break;

  default:
    exit(1);
  }
}

void storing_layer_end() {}