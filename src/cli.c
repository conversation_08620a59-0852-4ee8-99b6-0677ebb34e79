#include "cli.h"

#include "argtable3.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

static void print_help(const char *in_program, void *argtable[]) {
  log_info("Usage: %s", in_program);
  arg_print_syntax(stdout, argtable, "\n");
  arg_print_glossary(stdout, argtable, "  %-43s %s\n");
  log_space(LOG_INFO);
}

int cli_parse(CLIArguments *cli_arguments, const int argc, const char **argv,
              const char *program) {
  struct arg_lit *cli_help;
  struct arg_str *cli_config;
  struct arg_end *cli_end;

  void *argtable[] = {
      cli_help = arg_litn(CLI_HELP_SHORT, CLI_HELP, 0, 1, "print this help"),
      cli_config =
          arg_strn(CLI_CONFIG_SHORT, CLI_CONFIG, "<path>", 1, 1,
                   "unified configuration file path"),
      cli_end = arg_end(20)};

  if (arg_nullcheck(argtable)) {
    log_error("cli contains NULL values");
    return 1;
  }

  if (arg_parse(argc, (char **)argv, argtable) > 0) {
    arg_print_errors(stdout, cli_end, program);
    print_help(program, argtable);
    return 1;
  }

  if (cli_help->count > 0) {
    print_help(program, argtable);
    return 1;
  }

  cli_arguments->config_file = strdup(cli_config->sval[0]);

  arg_freetable(argtable, sizeof(argtable) / sizeof(argtable[0]));

  return 0;
}

void cli_destruct(CLIArguments *cli_arguments) {
  if (cli_arguments->config_file)
    free(cli_arguments->config_file);
}
