#include "cli.h"

#include "log.h"
#include <stdlib.h>
#include <string.h>

static void print_help(const char *program) {
  log_info("Usage: %s -c <config_file> [-h]", program);
  log_info("Options:");
  log_info("  -c, --config <path>    Unified configuration file path (required)");
  log_info("  -h, --help             Print this help");
  log_space(LOG_INFO);
}

int cli_parse(CLIArguments *cli_arguments, const int argc, const char **argv,
              const char *program) {
  cli_arguments->config_file = NULL;

  if (argc < 2) {
    log_error("Missing required arguments");
    print_help(program);
    return 1;
  }

  for (int i = 1; i < argc; i++) {
    if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
      print_help(program);
      return 1;
    } else if (strcmp(argv[i], "-c") == 0 || strcmp(argv[i], "--config") == 0) {
      if (i + 1 >= argc) {
        log_error("Option %s requires an argument", argv[i]);
        print_help(program);
        return 1;
      }
      cli_arguments->config_file = strdup(argv[++i]);
    } else {
      log_error("Unknown option: %s", argv[i]);
      print_help(program);
      return 1;
    }
  }

  if (!cli_arguments->config_file) {
    log_error("Missing required option: -c/--config");
    print_help(program);
    return 1;
  }

  return 0;
}

void cli_destruct(CLIArguments *cli_arguments) {
  if (cli_arguments->config_file)
    free(cli_arguments->config_file);
}
